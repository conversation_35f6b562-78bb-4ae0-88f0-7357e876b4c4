// Payment types for Zenera platform
// Based on zen-buy.be backend schemas

export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
  CANCELLED = 'cancelled',
}

export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  PAYPAL = 'paypal',
  BANK_TRANSFER = 'bank_transfer',
  CASH_ON_DELIVERY = 'cash_on_delivery',
  E_WALLET = 'e_wallet',
}

export interface PaymentDetails {
  card_last4?: string;
  card_brand?: string;
  paypal_payer_id?: string;
  bank_account?: string;
  wallet_provider?: string;
  [key: string]: any;
}

export interface Payment {
  _id?: string;
  id?: string;
  order_id: string;
  user_id: string;
  amount: number;
  currency: string;
  payment_method: PaymentMethod;
  payment_status: PaymentStatus;
  transaction_id?: string;
  payment_details?: PaymentDetails;
  error_message?: string;
  refund_amount?: number;
  refund_reason?: string;
  refunded_at?: Date;
  created_at?: Date;
  updated_at?: Date;
}

export interface CreatePaymentDto {
  order_id: string;
  user_id: string;
  amount: number;
  currency: string;
  payment_method: PaymentMethod;
  payment_details?: PaymentDetails;
}

export interface UpdatePaymentDto {
  payment_status?: PaymentStatus;
  transaction_id?: string;
  payment_details?: PaymentDetails;
  error_message?: string;
}

export interface RefundPaymentDto {
  refund_amount: number;
  refund_reason: string;
}

export interface PaymentFilters {
  user_id?: string;
  order_id?: string;
  payment_status?: PaymentStatus;
  payment_method?: PaymentMethod;
  amount_min?: number;
  amount_max?: number;
  created_after?: Date;
  created_before?: Date;
}

export interface PaymentStats {
  total_amount: number;
  total_transactions: number;
  successful_payments: number;
  failed_payments: number;
  refunded_amount: number;
  by_method: Record<PaymentMethod, number>;
  by_status: Record<PaymentStatus, number>;
}
