// Product types for Zenera platform
// Based on zen-buy.be backend schemas

export interface ProductAttributeValue {
  attribute_id: string;
  value: any;
}

export interface ProductAttribute {
  _id?: string;
  name: string;
  type: string;
  values?: string[];
}

export interface ProductVariant {
  _id?: string;
  product_id: string;
  sku: string;
  price: number;
  compare_at_price?: number;
  cost_price?: number;
  weight?: number;
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
  };
  attribute_values: ProductAttributeValue[];
  images?: string[];
  is_active?: boolean;
}

export interface Product {
  _id?: string;
  id?: string;
  name: string;
  slug: string;
  description: string;
  category_id: string;
  brand?: string;
  tags?: string[];
  images: string[];
  is_active?: boolean;
  meta_title?: string;
  meta_description?: string;
  attributes: ProductAttributeValue[];
  variant_attributes?: ProductAttribute[];
  has_variants?: boolean;
  base_price?: number;
  compare_at_price?: number;
  avg_rating: number;
  review_count: number;
  created_at?: Date;
  updated_at?: Date;
}
