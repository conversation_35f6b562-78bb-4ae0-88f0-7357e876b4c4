// Session types for Zenera platform
// Based on zen-buy.be backend schemas

export interface DeviceInfo {
  device_type: 'mobile' | 'tablet' | 'desktop';
  os: string;
  browser: string;
  ip_address: string;
  user_agent: string;
}

export interface Session {
  _id?: string;
  id?: string;
  user_id: string;
  token: string;
  device_info: DeviceInfo;
  is_active: boolean;
  last_activity: Date;
  expires_at: Date;
  created_at?: Date;
  updated_at?: Date;
}

export interface CreateSessionDto {
  user_id: string;
  token: string;
  device_info: DeviceInfo;
  expires_at: Date;
}

export interface UpdateSessionDto {
  is_active?: boolean;
  last_activity?: Date;
  expires_at?: Date;
}

export interface SessionFilters {
  user_id?: string;
  is_active?: boolean;
  device_type?: string;
  created_after?: Date;
  created_before?: Date;
}

export interface SessionStats {
  total_sessions: number;
  active_sessions: number;
  by_device_type: Record<string, number>;
  by_browser: Record<string, number>;
  by_os: Record<string, number>;
  average_session_duration: number;
}
