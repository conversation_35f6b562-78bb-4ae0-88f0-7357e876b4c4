// Inventory types for Zenera platform
// Based on zen-buy.be backend schemas

import { InventoryStatus } from './enums';

export interface Inventory {
  _id?: string;
  variant_id: string;
  warehouse_id: string;
  quantity: number;
  reserved_quantity: number;
  available_quantity: number;
  low_stock_threshold: number;
  reorder_quantity?: number;
  status: InventoryStatus;
  backorderable: boolean;
  next_available_date?: Date;
  location_data?: Record<string, any>;
  created_at?: Date;
  updated_at?: Date;
}
