// Order types for Zenera platform
// Based on zen-buy.be backend schemas

import { OrderStatus } from './enums';
import { PaymentMethod } from './payment';
import { ShippingAddress } from './shipping';
import { ProductAttributeValue } from './product';

export interface OrderItem {
  product_id: string;
  variant_id: string;
  quantity: number;
  price: number;
  // Populated fields for display
  product_name?: string;
  product_image?: string;
  variant_attributes?: ProductAttributeValue[];
}

export interface StatusHistoryItem {
  status: string;
  timestamp: Date;
  comment?: string;
  updated_by?: string;
}

export interface Order {
  _id?: string;
  id?: string;
  user_id: string;
  items: OrderItem[];
  total_price: number;
  shipping_address: ShippingAddress;
  payment_method: PaymentMethod;
  order_status: OrderStatus;
  status_history: StatusHistoryItem[];
  order_notes?: string;
  created_at?: Date;
  updated_at?: Date;
}
