// Promotion types for Zenera platform
// Based on zen-buy.be backend schemas

export enum PromotionType {
  PERCENTAGE_DISCOUNT = 'percentage_discount',
  FIXED_DISCOUNT = 'fixed_discount',
  FREE_SHIPPING = 'free_shipping',
  BUY_X_GET_Y = 'buy_x_get_y',
  BUNDLE_DISCOUNT = 'bundle_discount',
}

export enum CouponType {
  PERCENTAGE = 'percentage',
  FIXED = 'fixed',
}

export enum PromotionRuleType {
  MIN_SPEND = 'min_spend',
  MIN_QUANTITY = 'min_quantity',
  SPECIFIC_PRODUCTS = 'specific_products',
  SPECIFIC_CATEGORIES = 'specific_categories',
  FIRST_TIME_PURCHASE = 'first_time_purchase',
  REPEAT_PURCHASE = 'repeat_purchase',
}

export enum PromotionBenefitType {
  PERCENTAGE_DISCOUNT = 'percentage_discount',
  FIXED_DISCOUNT = 'fixed_discount',
  FREE_PRODUCT = 'free_product',
  FREE_SHIPPING = 'free_shipping',
}

export interface PromotionRule {
  type: PromotionRuleType;
  value: any;
}

export interface PromotionBenefit {
  type: PromotionBenefitType;
  value: any;
  target_ids?: string[];
}

export interface Promotion {
  _id?: string;
  id?: string;
  name: string;
  type: PromotionType;
  description?: string;
  start_date: Date;
  end_date: Date;
  rules: PromotionRule[];
  benefits: PromotionBenefit[];
  usage_limit?: number;
  used_count: number;
  per_customer_limit?: number;
  priority: number;
  combinable: boolean;
  is_active: boolean;
  created_at?: Date;
  updated_at?: Date;
}

export interface Coupon {
  _id?: string;
  id?: string;
  code: string;
  type: CouponType;
  value: number;
  min_purchase_amount?: number;
  max_discount_amount?: number;
  start_date: Date;
  end_date: Date;
  usage_limit?: number;
  used_count: number;
  per_user_limit?: number;
  applicable_categories?: string[];
  excluded_categories?: string[];
  applicable_products?: string[];
  excluded_products?: string[];
  is_active: boolean;
  created_at?: Date;
  updated_at?: Date;
}

export interface CouponUsage {
  _id?: string;
  id?: string;
  coupon_id: string;
  user_id: string;
  order_id: string;
  discount_amount: number;
  used_at: Date;
}

export interface CreatePromotionDto {
  name: string;
  type: PromotionType;
  description?: string;
  start_date: Date;
  end_date: Date;
  rules: PromotionRule[];
  benefits: PromotionBenefit[];
  usage_limit?: number;
  per_customer_limit?: number;
  priority?: number;
  combinable?: boolean;
}

export interface CreateCouponDto {
  code: string;
  type: CouponType;
  value: number;
  min_purchase_amount?: number;
  max_discount_amount?: number;
  start_date: Date;
  end_date: Date;
  usage_limit?: number;
  per_user_limit?: number;
  applicable_categories?: string[];
  excluded_categories?: string[];
  applicable_products?: string[];
  excluded_products?: string[];
}

export interface PromotionFilters {
  type?: PromotionType;
  is_active?: boolean;
  start_date_after?: Date;
  end_date_before?: Date;
}

export interface CouponFilters {
  code?: string;
  type?: CouponType;
  is_active?: boolean;
  start_date_after?: Date;
  end_date_before?: Date;
}

export interface DiscountCalculation {
  original_amount: number;
  discount_amount: number;
  final_amount: number;
  applied_promotions: Promotion[];
  applied_coupons: Coupon[];
}
