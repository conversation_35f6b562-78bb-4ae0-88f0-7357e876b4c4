// Analytics types for Zenera platform
// Based on zen-buy.be backend schemas

export enum AnalyticsEventType {
  PAGE_VIEW = 'page_view',
  PRODUCT_VIEW = 'product_view',
  ADD_TO_CART = 'add_to_cart',
  REMOVE_FROM_CART = 'remove_from_cart',
  PURCHASE = 'purchase',
  SEARCH = 'search',
  CLICK = 'click',
  SIGNUP = 'signup',
  LOGIN = 'login',
}

export enum AnalyticsPeriod {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export interface AnalyticsEvent {
  _id?: string;
  id?: string;
  user_id?: string;
  session_id: string;
  event_type: AnalyticsEventType;
  page_url?: string;
  product_id?: string;
  category_id?: string;
  search_query?: string;
  revenue?: number;
  properties?: Record<string, any>;
  user_agent?: string;
  ip_address?: string;
  referrer?: string;
  timestamp: Date;
}

export interface SalesAnalytics {
  period: AnalyticsPeriod;
  start_date: Date;
  end_date: Date;
  total_revenue: number;
  total_orders: number;
  average_order_value: number;
  conversion_rate: number;
  new_customers: number;
  returning_customers: number;
  top_products: ProductAnalytics[];
  top_categories: CategoryAnalytics[];
  revenue_by_day: DailyRevenue[];
}

export interface ProductAnalytics {
  product_id: string;
  product_name: string;
  views: number;
  add_to_cart: number;
  purchases: number;
  revenue: number;
  conversion_rate: number;
}

export interface CategoryAnalytics {
  category_id: string;
  category_name: string;
  views: number;
  purchases: number;
  revenue: number;
  product_count: number;
}

export interface DailyRevenue {
  date: Date;
  revenue: number;
  orders: number;
  customers: number;
}

export interface UserAnalytics {
  period: AnalyticsPeriod;
  start_date: Date;
  end_date: Date;
  total_users: number;
  new_users: number;
  active_users: number;
  returning_users: number;
  user_retention_rate: number;
  average_session_duration: number;
  bounce_rate: number;
  top_pages: PageAnalytics[];
  user_demographics: UserDemographics;
}

export interface PageAnalytics {
  page_url: string;
  page_title?: string;
  views: number;
  unique_views: number;
  average_time_on_page: number;
  bounce_rate: number;
}

export interface UserDemographics {
  by_country: Record<string, number>;
  by_age_group: Record<string, number>;
  by_gender: Record<string, number>;
  by_device: Record<string, number>;
}

export interface InventoryAnalytics {
  total_products: number;
  in_stock_products: number;
  out_of_stock_products: number;
  low_stock_products: number;
  total_inventory_value: number;
  top_selling_products: ProductAnalytics[];
  slow_moving_products: ProductAnalytics[];
  inventory_turnover_rate: number;
}

export interface SellerAnalytics {
  seller_id: string;
  seller_name: string;
  total_products: number;
  total_sales: number;
  total_revenue: number;
  average_rating: number;
  total_reviews: number;
  commission_earned: number;
  top_products: ProductAnalytics[];
}

export interface DashboardMetrics {
  sales: SalesAnalytics;
  users: UserAnalytics;
  inventory: InventoryAnalytics;
  recent_orders: any[]; // Will reference Order type
  alerts: AnalyticsAlert[];
}

export interface AnalyticsAlert {
  type: 'low_stock' | 'high_refund_rate' | 'unusual_activity' | 'system_error';
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  created_at: Date;
  resolved?: boolean;
}
