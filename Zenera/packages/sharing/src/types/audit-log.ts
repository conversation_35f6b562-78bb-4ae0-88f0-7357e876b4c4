// Audit Log types for Zenera platform
// Based on zen-buy.be backend schemas

export enum AuditLogAction {
  // User actions
  USER_LOGIN = 'USER_LOGIN',
  USER_LOGOUT = 'USER_LOGOUT',
  USER_REGISTER = 'USER_REGISTER',
  USER_UPDATE = 'USER_UPDATE',
  USER_DELETE = 'USER_DELETE',
  USER_PASSWORD_CHANGE = 'USER_PASSWORD_CHANGE',
  USER_EMAIL_VERIFY = 'USER_EMAIL_VERIFY',
  
  // Product actions
  PRODUCT_CREATE = 'PRODUCT_CREATE',
  PRODUCT_UPDATE = 'PRODUCT_UPDATE',
  PRODUCT_DELETE = 'PRODUCT_DELETE',
  PRODUCT_APPROVE = 'PRODUCT_APPROVE',
  PRODUCT_REJECT = 'PRODUCT_REJECT',
  
  // Order actions
  ORDER_CREATE = 'ORDER_CREATE',
  ORDER_UPDATE = 'ORDER_UPDATE',
  ORDER_CANCEL = 'ORDER_CANCEL',
  ORDER_FULFILL = 'ORDER_FULFILL',
  ORDER_REFUND = 'ORDER_REFUND',
  
  // Payment actions
  PAYMENT_CREATE = 'PAYMENT_CREATE',
  PAYMENT_SUCCESS = 'PAYMENT_SUCCESS',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  PAYMENT_REFUND = 'PAYMENT_REFUND',
  
  // Admin actions
  ADMIN_LOGIN = 'ADMIN_LOGIN',
  ADMIN_USER_UPDATE = 'ADMIN_USER_UPDATE',
  ADMIN_SYSTEM_CONFIG = 'ADMIN_SYSTEM_CONFIG',
  
  // Seller actions
  SELLER_REGISTER = 'SELLER_REGISTER',
  SELLER_APPROVE = 'SELLER_APPROVE',
  SELLER_REJECT = 'SELLER_REJECT',
  SELLER_UPGRADE_SUCCESS = 'SELLER_UPGRADE_SUCCESS',
  SELLER_UPGRADE_FAILED = 'SELLER_UPGRADE_FAILED',
}

export interface AuditLog {
  _id?: string;
  id?: string;
  user_id?: string;
  action: AuditLogAction;
  entity: string;
  entity_id: string;
  old_data?: any;
  new_data?: any;
  ip_address?: string;
  user_agent?: string;
  metadata?: Record<string, any>;
  created_at?: Date;
  updated_at?: Date;
}

export interface CreateAuditLogDto {
  user_id?: string;
  action: AuditLogAction;
  entity: string;
  entity_id: string;
  old_data?: any;
  new_data?: any;
  ip_address?: string;
  user_agent?: string;
  metadata?: Record<string, any>;
}

export interface AuditLogFilters {
  user_id?: string;
  action?: AuditLogAction;
  entity?: string;
  entity_id?: string;
  created_after?: Date;
  created_before?: Date;
  ip_address?: string;
}

export interface AuditLogStats {
  total_logs: number;
  by_action: Record<AuditLogAction, number>;
  by_entity: Record<string, number>;
  by_user: Record<string, number>;
  recent_activities: AuditLog[];
}
