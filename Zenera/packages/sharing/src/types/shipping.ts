// Shipping types for Zenera platform
// Based on zen-buy.be backend schemas

export enum ShippingMethod {
  STANDARD = 'standard',
  EXPRESS = 'express',
  SAME_DAY = 'same_day',
  OVERNIGHT = 'overnight',
}

export enum ShippingStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  IN_TRANSIT = 'in_transit',
  OUT_FOR_DELIVERY = 'out_for_delivery',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  RETURNED = 'returned',
}

export interface ShippingAddress {
  recipient_name: string;
  street: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  phone: string;
  email?: string;
}

export interface ShippingDimensions {
  length: number;
  width: number;
  height: number;
  unit?: 'cm' | 'inch';
}

export interface ShippingZone {
  name: string;
  countries: string[];
  base_rate: number;
  weight_based_rates: {
    min_weight: number;
    max_weight: number;
    rate: number;
  }[];
}

export interface TrackingEvent {
  status: string;
  description: string;
  location?: string;
  timestamp: Date;
}

export interface Shipping {
  _id?: string;
  id?: string;
  order_id: string;
  method: ShippingMethod;
  status: ShippingStatus;
  tracking_number?: string;
  carrier?: string;
  estimated_delivery?: Date;
  actual_delivery?: Date;
  shipping_address: ShippingAddress;
  shipping_cost: number;
  weight: number;
  dimensions?: ShippingDimensions;
  tracking_events?: TrackingEvent[];
  notes?: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface CreateShippingDto {
  order_id: string;
  method: ShippingMethod;
  shipping_address: ShippingAddress;
  shipping_cost: number;
  weight: number;
  dimensions?: ShippingDimensions;
  estimated_delivery?: Date;
  notes?: string;
}

export interface UpdateShippingDto {
  status?: ShippingStatus;
  tracking_number?: string;
  carrier?: string;
  estimated_delivery?: Date;
  actual_delivery?: Date;
  notes?: string;
}

export interface ShippingFilters {
  order_id?: string;
  status?: ShippingStatus;
  method?: ShippingMethod;
  carrier?: string;
  created_after?: Date;
  created_before?: Date;
}

export interface ShippingRate {
  method: ShippingMethod;
  cost: number;
  estimated_days: number;
  carrier: string;
}

export interface ShippingCalculation {
  weight: number;
  dimensions?: ShippingDimensions;
  origin_address: ShippingAddress;
  destination_address: ShippingAddress;
  rates: ShippingRate[];
}
