// Search types for Zenera platform
// Based on zen-buy.be backend schemas

export interface SearchFilters {
  category_ids?: string[];
  price_min?: number;
  price_max?: number;
  brand?: string;
  rating_min?: number;
  in_stock?: boolean;
  tags?: string[];
  seller_id?: string;
  location?: string;
  sort_by?: 'relevance' | 'price_asc' | 'price_desc' | 'rating' | 'newest' | 'popularity';
}

export interface SearchQuery {
  query: string;
  filters?: SearchFilters;
  page?: number;
  limit?: number;
  include_suggestions?: boolean;
}

export interface SearchResult {
  products: any[]; // Will reference Product type
  total: number;
  page: number;
  limit: number;
  total_pages: number;
  suggestions?: string[];
  facets?: SearchFacets;
}

export interface SearchFacets {
  categories: FacetItem[];
  brands: FacetItem[];
  price_ranges: PriceRangeFacet[];
  ratings: FacetItem[];
  tags: FacetItem[];
}

export interface FacetItem {
  value: string;
  count: number;
  selected?: boolean;
}

export interface PriceRangeFacet {
  min: number;
  max: number;
  count: number;
  selected?: boolean;
}

export interface SearchSuggestion {
  _id?: string;
  id?: string;
  query: string;
  count: number;
  last_searched: Date;
}

export interface SearchHistory {
  _id?: string;
  id?: string;
  user_id: string;
  query: string;
  filters?: SearchFilters;
  results_count: number;
  clicked_product_id?: string;
  searched_at: Date;
}

export interface PopularSearch {
  query: string;
  search_count: number;
  period: 'daily' | 'weekly' | 'monthly';
}

export interface SearchAnalytics {
  total_searches: number;
  unique_queries: number;
  zero_result_queries: number;
  popular_queries: PopularSearch[];
  trending_queries: PopularSearch[];
  conversion_rate: number;
}
