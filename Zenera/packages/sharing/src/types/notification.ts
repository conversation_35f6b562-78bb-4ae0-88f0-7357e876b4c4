// Notification types for Zenera platform
// Based on zen-buy.be backend schemas

export enum NotificationType {
  ORDER_STATUS = 'order_status',
  PAYMENT_STATUS = 'payment_status',
  SHIPPING_UPDATE = 'shipping_update',
  PRODUCT_REVIEW = 'product_review',
  SYSTEM = 'system',
  INVENTORY_ALERT = 'inventory_alert',
  ORDER_CREATED = 'order_created',
  ORDER_CANCELLED = 'order_cancelled',
}

export enum NotificationStatus {
  UNREAD = 'unread',
  READ = 'read',
  ARCHIVED = 'archived',
}

export interface NotificationData {
  order_id?: string;
  product_id?: string;
  payment_id?: string;
  [key: string]: any;
}

export interface Notification {
  _id?: string;
  id?: string;
  user_id: string;
  type: NotificationType;
  title: string;
  message: string;
  status: NotificationStatus;
  data?: NotificationData;
  created_at?: Date;
  updated_at?: Date;
}

export interface CreateNotificationDto {
  user_id: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: NotificationData;
}

export interface UpdateNotificationDto {
  status?: NotificationStatus;
  title?: string;
  message?: string;
  data?: NotificationData;
}

export interface NotificationFilters {
  user_id?: string;
  type?: NotificationType;
  status?: NotificationStatus;
  created_after?: Date;
  created_before?: Date;
}

export interface NotificationStats {
  total: number;
  unread: number;
  read: number;
  archived: number;
}
