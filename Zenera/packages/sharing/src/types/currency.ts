// Currency types for Zenera platform
// Based on zen-buy.be backend schemas

export interface Currency {
  _id?: string;
  id?: string;
  code: string; // ISO 4217 currency code (USD, EUR, VND, etc.)
  name: string;
  symbol: string;
  decimal_places: number;
  exchange_rate: number; // Rate relative to base currency
  is_base_currency: boolean;
  is_active: boolean;
  created_at?: Date;
  updated_at?: Date;
}

export interface ExchangeRate {
  _id?: string;
  id?: string;
  from_currency: string;
  to_currency: string;
  rate: number;
  source: string; // API source or manual
  last_updated: Date;
  created_at?: Date;
}

export interface CurrencyConversion {
  from_currency: string;
  to_currency: string;
  amount: number;
  converted_amount: number;
  exchange_rate: number;
  conversion_date: Date;
}

export interface CreateCurrencyDto {
  code: string;
  name: string;
  symbol: string;
  decimal_places: number;
  exchange_rate: number;
  is_base_currency?: boolean;
}

export interface UpdateCurrencyDto {
  name?: string;
  symbol?: string;
  decimal_places?: number;
  exchange_rate?: number;
  is_base_currency?: boolean;
  is_active?: boolean;
}

export interface CurrencyFilters {
  is_active?: boolean;
  is_base_currency?: boolean;
  code?: string;
}

export interface CurrencyStats {
  total_currencies: number;
  active_currencies: number;
  base_currency: Currency;
  most_used_currencies: {
    currency_code: string;
    usage_count: number;
  }[];
}
