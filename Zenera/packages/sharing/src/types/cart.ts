// Cart types for Zenera platform
// Based on zen-buy.be backend schemas

import { ProductAttributeValue } from './product';

export interface CartItem {
  _id?: string;
  product_id: string;
  variant_id?: string;
  quantity: number;
  price: number;
  // Populated fields for display
  product_name?: string;
  product_image?: string;
  product_slug?: string;
  variant_attributes?: ProductAttributeValue[];
  is_available?: boolean;
}

export interface Cart {
  _id?: string;
  user_id?: string;
  items: CartItem[];
  created_at?: Date;
  updated_at?: Date;
}

export interface CartSummary {
  items: CartItem[];
  total_items: number;
  total_price: number;
  shipping_fee?: number;
  discount_amount?: number;
  final_total: number;
}
