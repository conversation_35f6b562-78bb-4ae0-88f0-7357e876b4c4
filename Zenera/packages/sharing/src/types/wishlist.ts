// Wishlist types for Zenera platform
// Based on zen-buy.be backend schemas

export enum WishlistPrivacy {
  PRIVATE = 'private',
  PUBLIC = 'public',
  SHARED = 'shared',
}

export interface WishlistItem {
  _id?: string;
  id?: string;
  product_id: string;
  variant_id?: string;
  added_at: Date;
  notes?: string;
  priority?: number;
}

export interface Wishlist {
  _id?: string;
  id?: string;
  user_id: string;
  name: string;
  description?: string;
  privacy: WishlistPrivacy;
  items: WishlistItem[];
  is_default: boolean;
  shared_with?: string[]; // User IDs
  created_at?: Date;
  updated_at?: Date;
}

export interface CreateWishlistDto {
  name: string;
  description?: string;
  privacy?: WishlistPrivacy;
  is_default?: boolean;
}

export interface UpdateWishlistDto {
  name?: string;
  description?: string;
  privacy?: WishlistPrivacy;
  is_default?: boolean;
}

export interface AddToWishlistDto {
  product_id: string;
  variant_id?: string;
  notes?: string;
  priority?: number;
}

export interface UpdateWishlistItemDto {
  notes?: string;
  priority?: number;
}

export interface WishlistFilters {
  user_id?: string;
  privacy?: WishlistPrivacy;
  is_default?: boolean;
  created_after?: Date;
  created_before?: Date;
}

export interface WishlistStats {
  total_wishlists: number;
  total_items: number;
  most_wished_products: {
    product_id: string;
    product_name: string;
    wish_count: number;
  }[];
  user_wishlist_stats: {
    user_id: string;
    wishlist_count: number;
    total_items: number;
  }[];
}

export interface SharedWishlist {
  wishlist_id: string;
  wishlist_name: string;
  owner_id: string;
  owner_name: string;
  shared_at: Date;
  can_edit: boolean;
}
