// Warehouse types for Zenera platform
// Based on zen-buy.be backend schemas

export enum WarehouseStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  MAINTENANCE = 'maintenance',
}

export interface WarehouseAddress {
  street: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  latitude?: number;
  longitude?: number;
}

export interface Warehouse {
  _id?: string;
  id?: string;
  name: string;
  code: string;
  description?: string;
  address: WarehouseAddress;
  contact_person: string;
  contact_phone: string;
  contact_email: string;
  status: WarehouseStatus;
  capacity: number;
  current_utilization: number;
  operating_hours: {
    monday: { open: string; close: string; };
    tuesday: { open: string; close: string; };
    wednesday: { open: string; close: string; };
    thursday: { open: string; close: string; };
    friday: { open: string; close: string; };
    saturday: { open: string; close: string; };
    sunday: { open: string; close: string; };
  };
  is_default: boolean;
  created_at?: Date;
  updated_at?: Date;
}

export interface WarehouseInventory {
  _id?: string;
  id?: string;
  warehouse_id: string;
  product_id: string;
  variant_id?: string;
  quantity: number;
  reserved_quantity: number;
  available_quantity: number;
  location_code?: string;
  aisle?: string;
  shelf?: string;
  bin?: string;
  last_counted_at?: Date;
  created_at?: Date;
  updated_at?: Date;
}

export interface StockMovement {
  _id?: string;
  id?: string;
  warehouse_id: string;
  product_id: string;
  variant_id?: string;
  movement_type: 'in' | 'out' | 'transfer' | 'adjustment';
  quantity: number;
  reference_type: 'purchase' | 'sale' | 'transfer' | 'adjustment' | 'return';
  reference_id?: string;
  notes?: string;
  performed_by: string;
  created_at?: Date;
}

export interface WarehouseTransfer {
  _id?: string;
  id?: string;
  from_warehouse_id: string;
  to_warehouse_id: string;
  product_id: string;
  variant_id?: string;
  quantity: number;
  status: 'pending' | 'in_transit' | 'completed' | 'cancelled';
  requested_by: string;
  approved_by?: string;
  shipped_at?: Date;
  received_at?: Date;
  tracking_number?: string;
  notes?: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface CreateWarehouseDto {
  name: string;
  code: string;
  description?: string;
  address: WarehouseAddress;
  contact_person: string;
  contact_phone: string;
  contact_email: string;
  capacity: number;
  operating_hours: Warehouse['operating_hours'];
}

export interface UpdateWarehouseDto {
  name?: string;
  description?: string;
  address?: WarehouseAddress;
  contact_person?: string;
  contact_phone?: string;
  contact_email?: string;
  status?: WarehouseStatus;
  capacity?: number;
  operating_hours?: Warehouse['operating_hours'];
}

export interface WarehouseFilters {
  status?: WarehouseStatus;
  city?: string;
  state?: string;
  country?: string;
}

export interface WarehouseStats {
  total_warehouses: number;
  active_warehouses: number;
  total_capacity: number;
  total_utilization: number;
  average_utilization: number;
  top_warehouses: {
    warehouse_id: string;
    warehouse_name: string;
    utilization: number;
    inventory_value: number;
  }[];
}
