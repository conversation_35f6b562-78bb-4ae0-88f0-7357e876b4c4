// User types for Zenera platform
// Based on zen-buy.be backend schemas

import { Role, UserStatus } from './enums';

export interface CustomerInfo {
  points: number;
  addresses: string[];
  default_address?: string;
}

export interface SellerInfo {
  business_name: string;
  business_type: string;
  business_license?: string;
  tax_id?: string;
  business_address: string;
  bank_account_number?: string;
  bank_name?: string;
  is_approved: boolean;
  approval_date?: Date;
  commission_rate: number;
}

export interface AdminInfo {
  admin_role: string;
  permissions: string[];
  department?: string;
}

export interface User {
  _id?: string;
  id?: string;
  email: string;
  username?: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
  roles: Role[];
  permissions?: string[];
  status?: UserStatus;
  is_active?: boolean;
  is_verified?: boolean;
  avatar_url?: string;
  last_login_at?: Date;
  customer_info?: CustomerInfo;
  seller_info?: SellerInfo;
  admin_info?: AdminInfo;
  created_at?: Date;
  updated_at?: Date;
}
