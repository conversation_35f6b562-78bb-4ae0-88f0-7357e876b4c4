# Zenera Styles System Guide

## Tổng quan

Hệ thống styles của Zenera được thiết kế với triết lý "Metal UI" - một design system hiện đại với các hiệu ứng kim lo<PERSON>i, animation mượt mà và trải nghiệm người dùng cao cấp.

## C<PERSON>u trúc thư mục

```
src/styles/
├── zenera.scss              # File chính import tất cả styles
├── _variables.scss          # Biến SCSS và functions
├── animations/
│   ├── _keyframes.scss      # Định nghĩa @keyframes
│   ├── _mixins.scss         # Mixins cho animations
│   └── _utilities.scss      # Utility classes cho animations
└── components/              # Component-specific styles (sẽ thêm sau)
```

## Hệ thống màu sắc

### Palette chính
- **Primary**: Các tông màu xanh dương (#0066CC đến #004499)
- **Secondary**: <PERSON><PERSON><PERSON> tông màu x<PERSON><PERSON> kim lo<PERSON> (#6B7280 đến #374151)
- **Accent**: <PERSON><PERSON><PERSON> nhấn vàng kim (#F59E0B đến #D97706)
- **Success**: Xanh lá (#10B981 đến #059669)
- **Warning**: Cam (#F59E0B đến #D97706)
- **Error**: Đỏ (#EF4444 đến #DC2626)

### Sử dụng màu sắc

```scss
// Trong SCSS
.my-component {
  background-color: zenera-color(primary, 500);
  color: zenera-color(neutral, 100);
}

// Trong CSS classes
.bg-primary-500 { background-color: var(--zenera-primary-500); }
.text-neutral-100 { color: var(--zenera-neutral-100); }
```

## Hệ thống Typography

### Font families
- **Primary**: Inter (sans-serif hiện đại)
- **Secondary**: Roboto (fallback)
- **Monospace**: 'Fira Code', 'Courier New'

### Font sizes
- xs: 0.75rem (12px)
- sm: 0.875rem (14px)
- base: 1rem (16px)
- lg: 1.125rem (18px)
- xl: 1.25rem (20px)
- 2xl: 1.5rem (24px)
- 3xl: 1.875rem (30px)
- 4xl: 2.25rem (36px)

## Hệ thống Spacing

Sử dụng scale 8px:
- 1: 0.25rem (4px)
- 2: 0.5rem (8px)
- 3: 0.75rem (12px)
- 4: 1rem (16px)
- 5: 1.25rem (20px)
- 6: 1.5rem (24px)
- 8: 2rem (32px)
- 10: 2.5rem (40px)
- 12: 3rem (48px)
- 16: 4rem (64px)

## Hệ thống Shadows

### Zenera shadows
```scss
// Sử dụng function
.card {
  box-shadow: zenera-shadow(md);
}

// Hoặc CSS variables
.card {
  box-shadow: var(--zenera-shadow-md);
}
```

### Levels
- **sm**: Subtle shadow cho hover states
- **md**: Standard shadow cho cards
- **lg**: Prominent shadow cho modals
- **xl**: Dramatic shadow cho floating elements

## Animation System

### Durations
- **fast**: 150ms - Micro-interactions
- **normal**: 300ms - Standard transitions
- **slow**: 500ms - Complex animations
- **slower**: 750ms - Page transitions

### Easing functions
- **ease-out**: Mặc định cho most transitions
- **ease-in**: Cho disappearing elements
- **ease-in-out**: Cho complex animations
- **bounce**: Cho playful interactions

### Metal Animations

#### 1. Metal Enter Animation
```scss
.element {
  @include zenera-metal-enter();
}
```

#### 2. Metal Hover Effect
```scss
.button {
  @include zenera-hover-metal();
}
```

#### 3. Utility Classes
```html
<!-- Fade animations -->
<div class="zenera-fade-in">Content</div>
<div class="zenera-fade-out">Content</div>

<!-- Slide animations -->
<div class="zenera-slide-up">Content</div>
<div class="zenera-slide-down">Content</div>

<!-- Scale animations -->
<div class="zenera-scale-in">Content</div>
<div class="zenera-scale-out">Content</div>

<!-- Metal effects -->
<div class="zenera-metal-shine">Content</div>
<div class="zenera-hover-lift">Content</div>
```

## Responsive Design

### Breakpoints
```scss
$zenera-breakpoints: (
  sm: 640px,
  md: 768px,
  lg: 1024px,
  xl: 1280px,
  2xl: 1536px
);
```

### Mixins
```scss
// Mobile only
@include zenera-mobile-only() {
  // Styles for mobile only
}

// Tablet and up
@include zenera-tablet-up() {
  // Styles for tablet and larger
}
```

## Component Styling Guidelines

### 1. BEM Methodology
```scss
.zenera-card {
  // Block styles
  
  &__header {
    // Element styles
  }
  
  &--featured {
    // Modifier styles
  }
}
```

### 2. CSS Custom Properties
```scss
.zenera-button {
  --button-bg: #{zenera-color(primary, 500)};
  --button-text: #{zenera-color(neutral, 100)};
  
  background-color: var(--button-bg);
  color: var(--button-text);
}
```

## Testing Styles

### 1. Visual Testing
```bash
# Start dev server
pnpm dev

# Open browser và test:
# - Responsive design
# - Hover states
# - Animation performance
# - Color contrast
```

### 2. Build Testing
```bash
# Test production build
pnpm build

# Check for:
# - SCSS compilation errors
# - Missing dependencies
# - CSS optimization
```

### 3. Performance Testing
- Kiểm tra CSS bundle size
- Test animation performance trên mobile
- Validate accessibility contrast ratios

## Best Practices

### 1. Performance
- Sử dụng `transform` và `opacity` cho animations
- Tránh animating layout properties
- Sử dụng `will-change` cẩn thận

### 2. Accessibility
- Maintain contrast ratios >= 4.5:1
- Provide `prefers-reduced-motion` alternatives
- Ensure focus states are visible

### 3. Maintainability
- Sử dụng design tokens thay vì hard-coded values
- Document custom properties
- Keep specificity low

## Troubleshooting

### Common Issues

1. **SCSS compilation errors**
   - Check import paths
   - Verify variable definitions
   - Ensure proper nesting

2. **Animation performance**
   - Use `transform3d()` for hardware acceleration
   - Check for layout thrashing
   - Optimize keyframes

3. **Color inconsistencies**
   - Always use design tokens
   - Check color function usage
   - Validate in different themes

## Next Steps

1. Implement dark theme support
2. Add more component-specific styles
3. Create style guide documentation
4. Set up visual regression testing
