// Zenera Animation Mixins - Easy to use and customize

// === ELEMENT-BASED ANIMATION MIXINS ===

// Metal Element Animations
@mixin zenera-metal-enter($duration: 300, $easing: sharp) {
  animation: zenera-metal-enter zenera-duration($duration) zenera-easing($easing) forwards;
}

@mixin zenera-metal-exit($duration: 300, $easing: sharp) {
  animation: zenera-metal-exit zenera-duration($duration) zenera-easing($easing) forwards;
}

// Wood Element Animations
@mixin zenera-wood-grow($duration: 500, $easing: out) {
  animation: zenera-wood-grow zenera-duration($duration) zenera-easing($easing) forwards;
}

@mixin zenera-wood-sway($duration: 1000, $easing: in-out) {
  animation: zenera-wood-sway zenera-duration($duration) zenera-easing($easing) infinite;
}

// Water Element Animations
@mixin zenera-water-flow($duration: 700, $easing: in-out) {
  animation: zenera-water-flow zenera-duration($duration) zenera-easing($easing) forwards;
}

@mixin zenera-water-ripple($duration: 500, $easing: out) {
  animation: zenera-water-ripple zenera-duration($duration) zenera-easing($easing) infinite;
}

// Fire Element Animations
@mixin zenera-fire-flicker($duration: 1000, $easing: in-out) {
  animation: zenera-fire-flicker zenera-duration($duration) zenera-easing($easing) infinite;
}

@mixin zenera-fire-burst($duration: 300, $easing: bounce) {
  animation: zenera-fire-burst zenera-duration($duration) zenera-easing($easing) forwards;
}

// Earth Element Animations
@mixin zenera-earth-settle($duration: 500, $easing: bounce) {
  animation: zenera-earth-settle zenera-duration($duration) zenera-easing($easing) forwards;
}

@mixin zenera-earth-rumble($duration: 200, $easing: linear) {
  animation: zenera-earth-rumble zenera-duration($duration) zenera-easing($easing) infinite;
}

// === SPECIALIZED ANIMATION MIXINS ===

@mixin zenera-angular-slide($duration: 300, $easing: sharp) {
  animation: zenera-angular-slide-in zenera-duration($duration) zenera-easing($easing) forwards;
}

@mixin zenera-blade-cut($duration: 200, $easing: sharp) {
  animation: zenera-blade-cut zenera-duration($duration) zenera-easing($easing) forwards;
}

@mixin zenera-crystal-form($duration: 500, $easing: out) {
  animation: zenera-crystal-form zenera-duration($duration) zenera-easing($easing) forwards;
}

@mixin zenera-shatter($duration: 300, $easing: in) {
  animation: zenera-shatter zenera-duration($duration) zenera-easing($easing) forwards;
}

// === HOVER EFFECT MIXINS ===

@mixin zenera-hover-metal($lift: 2px, $rotation: 1deg) {
  @include zenera-transition(all, 200, sharp);
  
  &:hover {
    transform: translateY(-$lift) rotate($rotation);
    @include zenera-shadow(lg);
    filter: brightness(1.05);
  }
}

@mixin zenera-hover-crystal($scale: 1.02) {
  @include zenera-transition(all, 300, out);
  
  &:hover {
    transform: scale($scale);
    @include zenera-shadow(md);
    box-shadow: 
      zenera-shadow(md),
      0 0 20px rgba(zenera-color($zenera-primary, 500), 0.3);
    filter: brightness(1.1);
  }
}

@mixin zenera-hover-water($skew: 2deg) {
  @include zenera-transition(all, 300, in-out);
  
  &:hover {
    transform: skewX($skew);
    @include zenera-shadow(md);
    filter: brightness(1.05);
  }
}

@mixin zenera-hover-fire($scale: 1.05) {
  @include zenera-transition(all, 200, bounce);
  
  &:hover {
    transform: scale($scale);
    filter: brightness(1.1) saturate(1.2);
    @include zenera-fire-flicker(500);
  }
}

@mixin zenera-hover-earth($lift: 1px) {
  @include zenera-transition(all, 300, out);
  
  &:hover {
    transform: translateY(-$lift);
    @include zenera-shadow(base);
  }
}

// === LOADING ANIMATION MIXINS ===

@mixin zenera-loading-blade() {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    animation: zenera-blade-sweep zenera-duration(1000) zenera-easing(in-out) infinite;
    transform: skewX(-15deg);
  }
}

@mixin zenera-loading-pulse($duration: 1000) {
  animation: zenera-loading-pulse zenera-duration($duration) zenera-easing(in-out) infinite;
}

@mixin zenera-skeleton-loading($base-color: #f3f4f6, $highlight-color: #e5e7eb) {
  background: linear-gradient(90deg, $base-color 25%, $highlight-color 50%, $base-color 75%);
  background-size: 200px 100%;
  animation: zenera-skeleton-loading zenera-duration(1000) zenera-easing(linear) infinite;
}

// === E-COMMERCE SPECIFIC MIXINS ===

@mixin zenera-add-to-cart-effect($duration: 200) {
  animation: zenera-add-to-cart zenera-duration($duration) zenera-easing(bounce);
}

@mixin zenera-price-change-effect($duration: 500) {
  animation: zenera-price-change zenera-duration($duration) zenera-easing(out);
}

@mixin zenera-stock-alert($duration: 1000) {
  animation: zenera-stock-alert zenera-duration($duration) zenera-easing(in-out) infinite;
}

@mixin zenera-notification-slide($duration: 300) {
  animation: zenera-notification-slide zenera-duration($duration) zenera-easing(out) forwards;
}

// === RESPONSIVE ANIMATION MIXINS ===

@mixin zenera-mobile-animation($animation) {
  @include zenera-mobile-only {
    // Reduce animation intensity on mobile
    animation-duration: calc(#{zenera-duration(300)} * 0.7);
    animation-timing-function: zenera-easing(out);
  }
}

@mixin zenera-reduced-motion() {
  @media (prefers-reduced-motion: reduce) {
    animation: none !important;
    transition: none !important;
  }
}

// === ANIMATION STATE MIXINS ===

@mixin zenera-animation-paused() {
  animation-play-state: paused;
}

@mixin zenera-animation-running() {
  animation-play-state: running;
}

@mixin zenera-animation-delay($delay: 100) {
  animation-delay: zenera-duration($delay);
}

// === COMPLEX ANIMATION SEQUENCES ===

@mixin zenera-entrance-sequence($element: metal, $delay: 0) {
  opacity: 0;
  animation-delay: zenera-duration($delay);
  
  @if $element == metal {
    @include zenera-metal-enter();
  } @else if $element == wood {
    @include zenera-wood-grow();
  } @else if $element == water {
    @include zenera-water-flow();
  } @else if $element == fire {
    @include zenera-fire-burst();
  } @else if $element == earth {
    @include zenera-earth-settle();
  }
}

@mixin zenera-exit-sequence($element: metal, $delay: 0) {
  animation-delay: zenera-duration($delay);
  
  @if $element == metal {
    @include zenera-metal-exit();
  } @else if $element == water {
    @include zenera-shatter();
  } @else {
    @include zenera-shatter();
  }
}

// === UTILITY MIXINS ===

@mixin zenera-animation-speed($speed: 1) {
  animation-duration: calc(var(--animation-duration, #{zenera-duration(300)}) / #{$speed});
}

@mixin zenera-animation-infinite() {
  animation-iteration-count: infinite;
}

@mixin zenera-animation-once() {
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
}
