// Zenera Animations - Five Elements (<PERSON><PERSON>nh) Inspired Keyframes
// Unique angular and sharp movements with elemental themes

// === METAL ELEMENT (Kim) - Sharp, precise, angular movements ===
@keyframes zenera-metal-enter {
  0% {
    transform: scale(0.8) rotate(-5deg);
    opacity: 0;
    clip-path: polygon(0 0, 0 0, 0 100%, 0% 100%);
  }
  50% {
    transform: scale(1.05) rotate(2deg);
    opacity: 0.7;
    clip-path: polygon(0 0, 70% 0, 50% 100%, 0% 100%);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  }
}

@keyframes zenera-metal-exit {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  }
  50% {
    transform: scale(1.05) rotate(-2deg);
    opacity: 0.7;
    clip-path: polygon(30% 0, 100% 0, 100% 100%, 50% 100%);
  }
  100% {
    transform: scale(0.8) rotate(5deg);
    opacity: 0;
    clip-path: polygon(100% 0, 100% 0, 100% 100%, 100% 100%);
  }
}

// === WOOD ELEMENT (Mộc) - Growing, organic movements ===
@keyframes zenera-wood-grow {
  0% {
    transform: scaleY(0) scaleX(0.8);
    transform-origin: bottom center;
    opacity: 0;
  }
  30% {
    transform: scaleY(0.6) scaleX(0.9);
    opacity: 0.6;
  }
  60% {
    transform: scaleY(1.1) scaleX(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scaleY(1) scaleX(1);
    opacity: 1;
  }
}

@keyframes zenera-wood-sway {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(1deg);
  }
  75% {
    transform: rotate(-1deg);
  }
}

// === WATER ELEMENT (Thủy) - Flowing, wave-like movements ===
@keyframes zenera-water-flow {
  0% {
    transform: translateX(-100%) skewX(10deg);
    opacity: 0;
  }
  30% {
    transform: translateX(-20%) skewX(5deg);
    opacity: 0.7;
  }
  60% {
    transform: translateX(10%) skewX(-2deg);
    opacity: 0.9;
  }
  100% {
    transform: translateX(0) skewX(0deg);
    opacity: 1;
  }
}

@keyframes zenera-water-ripple {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

// === FIRE ELEMENT (Hỏa) - Flickering, energetic movements ===
@keyframes zenera-fire-flicker {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    filter: brightness(1) hue-rotate(0deg);
  }
  25% {
    transform: scale(1.05) rotate(1deg);
    filter: brightness(1.1) hue-rotate(5deg);
  }
  50% {
    transform: scale(0.98) rotate(-1deg);
    filter: brightness(1.05) hue-rotate(-3deg);
  }
  75% {
    transform: scale(1.02) rotate(0.5deg);
    filter: brightness(1.08) hue-rotate(2deg);
  }
}

@keyframes zenera-fire-burst {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// === EARTH ELEMENT (Thổ) - Stable, grounded movements ===
@keyframes zenera-earth-settle {
  0% {
    transform: translateY(-20px) scale(1.1);
    opacity: 0;
  }
  50% {
    transform: translateY(5px) scale(0.98);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-2px) scale(1.01);
    opacity: 0.95;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes zenera-earth-rumble {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-1px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(1px);
  }
}

// === SPECIALIZED ANIMATIONS ===
@keyframes zenera-angular-slide-in {
  0% {
    transform: translateX(-100%) skewX(-15deg);
    opacity: 0;
  }
  60% {
    transform: translateX(5%) skewX(5deg);
    opacity: 0.8;
  }
  100% {
    transform: translateX(0) skewX(0deg);
    opacity: 1;
  }
}

@keyframes zenera-blade-cut {
  0% {
    clip-path: polygon(0 0, 0 0, 0 100%, 0 100%);
    transform: translateX(-10px);
  }
  50% {
    clip-path: polygon(0 0, 60% 0, 40% 100%, 0 100%);
    transform: translateX(2px);
  }
  100% {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    transform: translateX(0);
  }
}

@keyframes zenera-crystal-form {
  0% {
    transform: scale(0) rotate(45deg);
    opacity: 0;
    clip-path: polygon(50% 50%, 50% 50%, 50% 50%, 50% 50%);
  }
  25% {
    transform: scale(0.3) rotate(35deg);
    opacity: 0.3;
    clip-path: polygon(30% 30%, 70% 30%, 70% 70%, 30% 70%);
  }
  50% {
    transform: scale(0.7) rotate(10deg);
    opacity: 0.7;
    clip-path: polygon(10% 25%, 90% 25%, 75% 90%, 25% 90%);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }
}

@keyframes zenera-shatter {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    filter: blur(0px);
  }
  30% {
    transform: scale(1.05) rotate(2deg);
    opacity: 0.9;
    filter: blur(0.5px);
  }
  60% {
    transform: scale(0.95) rotate(-1deg);
    opacity: 0.6;
    filter: blur(1px);
  }
  100% {
    transform: scale(0.8) rotate(5deg);
    opacity: 0;
    filter: blur(2px);
  }
}

@keyframes zenera-blade-sweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

// === E-COMMERCE SPECIFIC ANIMATIONS ===
@keyframes zenera-add-to-cart {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes zenera-price-change {
  0% {
    transform: scale(1);
    color: inherit;
  }
  50% {
    transform: scale(1.05);
    color: zenera-color($zenera-success, 500);
  }
  100% {
    transform: scale(1);
    color: inherit;
  }
}

@keyframes zenera-stock-alert {
  0%, 100% {
    background-color: transparent;
  }
  50% {
    background-color: zenera-color($zenera-warning, 100);
  }
}

@keyframes zenera-notification-slide {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes zenera-loading-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes zenera-skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}
