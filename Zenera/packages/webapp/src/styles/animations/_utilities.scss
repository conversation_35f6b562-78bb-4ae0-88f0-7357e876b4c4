// Zenera Animation Utility Classes
// Easy to apply animations with simple class names

// === ELEMENT-BASED ANIMATION CLASSES ===

// Metal Element
.zenera-animate-metal-enter {
  @include zenera-metal-enter();
}

.zenera-animate-metal-exit {
  @include zenera-metal-exit();
}

// Wood Element
.zenera-animate-wood-grow {
  @include zenera-wood-grow();
}

.zenera-animate-wood-sway {
  @include zenera-wood-sway();
}

// Water Element
.zenera-animate-water-flow {
  @include zenera-water-flow();
}

.zenera-animate-water-ripple {
  @include zenera-water-ripple();
}

// Fire Element
.zenera-animate-fire-flicker {
  @include zenera-fire-flicker();
}

.zenera-animate-fire-burst {
  @include zenera-fire-burst();
}

// Earth Element
.zenera-animate-earth-settle {
  @include zenera-earth-settle();
}

.zenera-animate-earth-rumble {
  @include zenera-earth-rumble();
}

// === SPECIALIZED ANIMATION CLASSES ===

.zenera-animate-angular-slide {
  @include zenera-angular-slide();
}

.zenera-animate-blade-cut {
  @include zenera-blade-cut();
}

.zenera-animate-crystal-form {
  @include zenera-crystal-form();
}

.zenera-animate-shatter {
  @include zenera-shatter();
}

// === HOVER EFFECT CLASSES ===

.zenera-hover-metal {
  @include zenera-hover-metal();
}

.zenera-hover-crystal {
  @include zenera-hover-crystal();
}

.zenera-hover-water {
  @include zenera-hover-water();
}

.zenera-hover-fire {
  @include zenera-hover-fire();
}

.zenera-hover-earth {
  @include zenera-hover-earth();
}

// === LOADING ANIMATION CLASSES ===

.zenera-loading-blade {
  @include zenera-loading-blade();
}

.zenera-loading-pulse {
  @include zenera-loading-pulse();
}

.zenera-skeleton-loading {
  @include zenera-skeleton-loading();
}

// === E-COMMERCE SPECIFIC CLASSES ===

.zenera-add-to-cart {
  @include zenera-add-to-cart-effect();
}

.zenera-price-change {
  @include zenera-price-change-effect();
}

.zenera-stock-alert {
  @include zenera-stock-alert();
}

.zenera-notification-slide {
  @include zenera-notification-slide();
}

// === ANIMATION CONTROL CLASSES ===

.zenera-animation-paused {
  @include zenera-animation-paused();
}

.zenera-animation-running {
  @include zenera-animation-running();
}

// Animation Speed Classes
.zenera-animation-slow {
  @include zenera-animation-speed(0.5);
}

.zenera-animation-fast {
  @include zenera-animation-speed(2);
}

.zenera-animation-disabled {
  animation: none !important;
  transition: none !important;
}

// Animation Delay Classes
.zenera-delay-75 {
  @include zenera-animation-delay(75);
}

.zenera-delay-100 {
  @include zenera-animation-delay(100);
}

.zenera-delay-150 {
  @include zenera-animation-delay(150);
}

.zenera-delay-200 {
  @include zenera-animation-delay(200);
}

.zenera-delay-300 {
  @include zenera-animation-delay(300);
}

.zenera-delay-500 {
  @include zenera-animation-delay(500);
}

// === ENTRANCE SEQUENCE CLASSES ===

.zenera-entrance-metal {
  @include zenera-entrance-sequence(metal);
}

.zenera-entrance-wood {
  @include zenera-entrance-sequence(wood);
}

.zenera-entrance-water {
  @include zenera-entrance-sequence(water);
}

.zenera-entrance-fire {
  @include zenera-entrance-sequence(fire);
}

.zenera-entrance-earth {
  @include zenera-entrance-sequence(earth);
}

// Staggered entrance animations
.zenera-entrance-stagger {
  > * {
    @include zenera-entrance-sequence(metal);
    
    @for $i from 1 through 10 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 100}ms;
      }
    }
  }
}

// === EXIT SEQUENCE CLASSES ===

.zenera-exit-metal {
  @include zenera-exit-sequence(metal);
}

.zenera-exit-water {
  @include zenera-exit-sequence(water);
}

// === RESPONSIVE ANIMATION CLASSES ===

.zenera-animate-mobile-only {
  @include zenera-mobile-only {
    @include zenera-metal-enter(200);
  }
  
  @include zenera-desktop-only {
    animation: none;
  }
}

.zenera-animate-desktop-only {
  @include zenera-mobile-only {
    animation: none;
  }
  
  @include zenera-desktop-only {
    @include zenera-metal-enter();
  }
}

// === ACCESSIBILITY CLASSES ===

.zenera-respect-motion {
  @include zenera-reduced-motion();
}

// === THEME-BASED ANIMATION CLASSES ===

.zenera-theme-professional {
  .zenera-animate-default {
    @include zenera-earth-settle();
  }
}

.zenera-theme-creative {
  .zenera-animate-default {
    @include zenera-fire-burst();
  }
}

.zenera-theme-minimal {
  .zenera-animate-default {
    @include zenera-metal-enter();
  }
}

.zenera-theme-luxury {
  .zenera-animate-default {
    @include zenera-crystal-form();
  }
}

// === INTERACTION CLASSES ===

.zenera-click-effect {
  position: relative;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
  }
  
  &:active::after {
    width: 200px;
    height: 200px;
  }
}

// === SCROLL-TRIGGERED ANIMATIONS ===

.zenera-scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all zenera-duration(500) zenera-easing(out);
  
  &.zenera-in-view {
    opacity: 1;
    transform: translateY(0);
  }
}

.zenera-scroll-reveal-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: all zenera-duration(500) zenera-easing(out);
  
  &.zenera-in-view {
    opacity: 1;
    transform: translateX(0);
  }
}

.zenera-scroll-reveal-right {
  opacity: 0;
  transform: translateX(30px);
  transition: all zenera-duration(500) zenera-easing(out);
  
  &.zenera-in-view {
    opacity: 1;
    transform: translateX(0);
  }
}

// === COMPONENT-SPECIFIC ANIMATIONS ===

// Product Card Animations
.zenera-product-card {
  @include zenera-hover-crystal();
  
  .zenera-product-image {
    @include zenera-transition(transform, 300, out);
  }
  
  &:hover .zenera-product-image {
    transform: scale(1.05);
  }
  
  .zenera-add-to-cart-btn {
    @include zenera-transition(all, 200, bounce);
    
    &:active {
      @include zenera-add-to-cart-effect();
    }
  }
}

// Button Animations
.zenera-btn {
  @include zenera-hover-metal();
  
  &.zenera-btn-primary {
    @include zenera-hover-fire();
  }
  
  &.zenera-btn-secondary {
    @include zenera-hover-earth();
  }
}

// Modal Animations
.zenera-modal-enter {
  @include zenera-crystal-form();
}

.zenera-modal-exit {
  @include zenera-shatter();
}

// Notification Animations
.zenera-notification {
  @include zenera-notification-slide();
}

// Loading States
.zenera-loading {
  @include zenera-loading-pulse();
}

.zenera-loading-skeleton {
  @include zenera-skeleton-loading();
}
