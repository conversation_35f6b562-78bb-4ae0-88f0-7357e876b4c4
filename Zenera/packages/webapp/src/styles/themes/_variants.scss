// Zenera Theme Variants - Different styles for different use cases

// === PROFESSIONAL THEME ===
$zenera-theme-professional: (
  primary: #1e40af,
  primary-hover: #1d4ed8,
  primary-active: #1e3a8a,
  accent: #374151,
  accent-hover: #4b5563,
  accent-active: #374151,
  
  button-radius: zenera-radius(sm),
  input-radius: zenera-radius(sm),
  card-radius: zenera-radius(md),
  
  animation-style: 'earth',
  animation-speed: 0.8,
);

.zenera-theme-professional {
  @include zenera-apply-theme($zenera-theme-professional);
  
  .zenera-btn {
    font-weight: zenera-font-weight(semibold);
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }
  
  .zenera-card {
    border-width: 1px;
    @include zenera-shadow(sm);
    
    &:hover {
      @include zenera-shadow(base);
      transform: translateY(-1px);
    }
  }
  
  .zenera-heading {
    font-weight: zenera-font-weight(extrabold);
  }
}

// === CREATIVE THEME ===
$zenera-theme-creative: (
  primary: #7c3aed,
  primary-hover: #8b5cf6,
  primary-active: #6d28d9,
  accent: #f59e0b,
  accent-hover: #fbbf24,
  accent-active: #d97706,
  
  button-radius: zenera-radius(xl),
  input-radius: zenera-radius(lg),
  card-radius: zenera-radius(2xl),
  
  animation-style: 'fire',
  animation-speed: 1.2,
);

.zenera-theme-creative {
  @include zenera-apply-theme($zenera-theme-creative);
  
  .zenera-btn {
    font-weight: zenera-font-weight(bold);
    background: linear-gradient(135deg, var(--zenera-primary), var(--zenera-accent));
    
    &:hover {
      background: linear-gradient(135deg, var(--zenera-primary-hover), var(--zenera-accent-hover));
      transform: translateY(-2px) scale(1.02);
    }
  }
  
  .zenera-card {
    background: linear-gradient(145deg, var(--zenera-card-bg), var(--zenera-bg-secondary));
    @include zenera-shadow(lg);
    
    &:hover {
      @include zenera-shadow(xl);
      transform: translateY(-4px) rotate(1deg);
    }
  }
  
  .zenera-input {
    border-width: 3px;
    
    &:focus {
      border-color: var(--zenera-accent);
      box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.2);
    }
  }
}

// === MINIMAL THEME ===
$zenera-theme-minimal: (
  primary: #000000,
  primary-hover: #1f2937,
  primary-active: #111827,
  accent: #6b7280,
  accent-hover: #9ca3af,
  accent-active: #4b5563,
  
  button-radius: zenera-radius(none),
  input-radius: zenera-radius(none),
  card-radius: zenera-radius(sm),
  
  card-shadow: none,
  
  animation-style: 'metal',
  animation-speed: 0.6,
);

.zenera-theme-minimal {
  @include zenera-apply-theme($zenera-theme-minimal);
  
  .zenera-btn {
    font-weight: zenera-font-weight(medium);
    text-transform: lowercase;
    border: 1px solid var(--zenera-border-primary);
    
    &:hover {
      background-color: var(--zenera-primary-hover);
      transform: none;
      box-shadow: none;
      border-color: var(--zenera-primary-hover);
    }
  }
  
  .zenera-card {
    border: 2px solid var(--zenera-border-primary);
    box-shadow: none;
    
    &:hover {
      border-color: var(--zenera-border-secondary);
      transform: none;
      box-shadow: none;
    }
  }
  
  .zenera-input {
    border: 2px solid var(--zenera-border-primary);
    
    &:focus {
      border-color: var(--zenera-primary);
      box-shadow: none;
    }
  }
  
  .zenera-heading {
    font-weight: zenera-font-weight(normal);
    text-transform: lowercase;
  }
}

// === LUXURY THEME ===
$zenera-theme-luxury: (
  primary: #92400e,
  primary-hover: #b45309,
  primary-active: #78350f,
  accent: #fbbf24,
  accent-hover: #f59e0b,
  accent-active: #d97706,
  
  button-radius: zenera-radius(lg),
  input-radius: zenera-radius(lg),
  card-radius: zenera-radius(xl),
  
  card-shadow: zenera-shadow(xl),
  
  animation-style: 'crystal',
  animation-speed: 1.5,
);

.zenera-theme-luxury {
  @include zenera-apply-theme($zenera-theme-luxury);
  
  .zenera-btn {
    font-weight: zenera-font-weight(semibold);
    background: linear-gradient(135deg, var(--zenera-primary), var(--zenera-accent));
    box-shadow: 0 4px 15px rgba(146, 64, 14, 0.3);
    
    &:hover {
      background: linear-gradient(135deg, var(--zenera-primary-hover), var(--zenera-accent-hover));
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(146, 64, 14, 0.4);
    }
  }
  
  .zenera-card {
    background: linear-gradient(145deg, var(--zenera-card-bg), rgba(251, 191, 36, 0.05));
    border: 1px solid rgba(251, 191, 36, 0.2);
    @include zenera-shadow(xl);
    
    &:hover {
      box-shadow: 0 25px 50px -12px rgba(146, 64, 14, 0.25);
      transform: translateY(-5px);
    }
  }
  
  .zenera-input {
    border: 2px solid rgba(251, 191, 36, 0.3);
    background: linear-gradient(145deg, var(--zenera-bg-primary), rgba(251, 191, 36, 0.02));
    
    &:focus {
      border-color: var(--zenera-accent);
      box-shadow: 0 0 0 4px rgba(251, 191, 36, 0.2);
    }
  }
  
  .zenera-heading {
    background: linear-gradient(135deg, var(--zenera-primary), var(--zenera-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

// === E-COMMERCE THEME ===
$zenera-theme-ecommerce: (
  primary: #059669,
  primary-hover: #047857,
  primary-active: #065f46,
  accent: #dc2626,
  accent-hover: #b91c1c,
  accent-active: #991b1b,
  
  button-radius: zenera-radius(md),
  input-radius: zenera-radius(md),
  card-radius: zenera-radius(lg),
  
  animation-style: 'water',
  animation-speed: 1,
);

.zenera-theme-ecommerce {
  @include zenera-apply-theme($zenera-theme-ecommerce);
  
  .zenera-btn {
    &.zenera-btn-add-to-cart {
      background-color: var(--zenera-primary);
      
      &:hover {
        background-color: var(--zenera-primary-hover);
        @include zenera-add-to-cart-effect();
      }
    }
    
    &.zenera-btn-buy-now {
      background-color: var(--zenera-accent);
      
      &:hover {
        background-color: var(--zenera-accent-hover);
      }
    }
  }
  
  .zenera-product-card {
    @include zenera-hover-crystal();
    
    .zenera-price {
      color: var(--zenera-primary);
      font-weight: zenera-font-weight(bold);
      
      &.zenera-price-sale {
        color: var(--zenera-accent);
        @include zenera-price-change-effect();
      }
    }
    
    .zenera-discount-badge {
      background-color: var(--zenera-accent);
      color: var(--zenera-text-inverse);
      border-radius: zenera-radius(full);
      padding: zenera-spacing(1) zenera-spacing(2);
      @include zenera-text(sm, bold);
    }
  }
  
  .zenera-cart-item {
    &.zenera-low-stock {
      @include zenera-stock-alert();
    }
  }
}

// === RESPONSIVE THEME ADJUSTMENTS ===
@include zenera-mobile-only {
  .zenera-theme {
    --zenera-card-radius: #{zenera-radius(md)};
    --zenera-button-radius: #{zenera-radius(md)};
    
    .zenera-btn {
      padding: zenera-spacing(3) zenera-spacing(4);
      @include zenera-text(base);
    }
    
    .zenera-card {
      padding: zenera-spacing(4);
    }
  }
  
  .zenera-theme-luxury {
    .zenera-btn {
      box-shadow: 0 2px 8px rgba(146, 64, 14, 0.2);
      
      &:hover {
        box-shadow: 0 4px 12px rgba(146, 64, 14, 0.3);
      }
    }
  }
}

// === THEME SWITCHER UTILITIES ===
.zenera-theme-switcher {
  position: fixed;
  top: zenera-spacing(4);
  right: zenera-spacing(4);
  z-index: zenera-z(fixed);
  display: flex;
  gap: zenera-spacing(2);
  
  button {
    width: 40px;
    height: 40px;
    border-radius: zenera-radius(full);
    border: 2px solid var(--zenera-border-primary);
    cursor: pointer;
    @include zenera-transition(all, 200, out);
    
    &:hover {
      transform: scale(1.1);
      @include zenera-shadow(md);
    }
    
    &.active {
      border-color: var(--zenera-primary);
      @include zenera-shadow(base);
    }
  }
  
  .theme-professional {
    background: linear-gradient(135deg, #1e40af, #374151);
  }
  
  .theme-creative {
    background: linear-gradient(135deg, #7c3aed, #f59e0b);
  }
  
  .theme-minimal {
    background: linear-gradient(135deg, #000000, #6b7280);
  }
  
  .theme-luxury {
    background: linear-gradient(135deg, #92400e, #fbbf24);
  }
  
  .theme-ecommerce {
    background: linear-gradient(135deg, #059669, #dc2626);
  }
}
