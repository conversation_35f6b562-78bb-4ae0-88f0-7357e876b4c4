// Zenera Base Theme - Foundation for all theme variants
// Easy to customize and extend

// === BASE THEME VARIABLES ===
$zenera-theme-base: (
  // Primary Colors
  primary: zenera-color($zenera-primary, 600),
  primary-hover: zenera-color($zenera-primary, 700),
  primary-active: zenera-color($zenera-primary, 800),
  primary-light: zenera-color($zenera-primary, 100),
  primary-dark: zenera-color($zenera-primary, 900),
  
  // Secondary Colors
  secondary: zenera-color($zenera-secondary, 500),
  secondary-hover: zenera-color($zenera-secondary, 600),
  secondary-active: zenera-color($zenera-secondary, 700),
  secondary-light: zenera-color($zenera-secondary, 100),
  secondary-dark: zenera-color($zenera-secondary, 800),
  
  // Accent Colors
  accent: zenera-color($zenera-accent, 500),
  accent-hover: zenera-color($zenera-accent, 600),
  accent-active: zenera-color($zenera-accent, 700),
  accent-light: zenera-color($zenera-accent, 100),
  accent-dark: zenera-color($zenera-accent, 800),
  
  // Background Colors
  bg-primary: #ffffff,
  bg-secondary: zenera-color($zenera-secondary, 50),
  bg-tertiary: zenera-color($zenera-secondary, 100),
  bg-overlay: rgba(0, 0, 0, 0.5),
  
  // Text Colors
  text-primary: zenera-color($zenera-secondary, 900),
  text-secondary: zenera-color($zenera-secondary, 600),
  text-tertiary: zenera-color($zenera-secondary, 400),
  text-inverse: #ffffff,
  
  // Border Colors
  border-primary: zenera-color($zenera-secondary, 200),
  border-secondary: zenera-color($zenera-secondary, 300),
  border-focus: zenera-color($zenera-primary, 500),
  
  // Component Specific
  card-bg: #ffffff,
  card-border: zenera-color($zenera-secondary, 200),
  card-shadow: zenera-shadow(sm),
  
  // Border Radius
  button-radius: zenera-radius(md),
  input-radius: zenera-radius(md),
  card-radius: zenera-radius(lg),
  
  // Animation Preferences
  animation-speed: 1,
  animation-style: 'metal',
);

// === DARK THEME VARIABLES ===
$zenera-theme-dark: (
  // Background Colors
  bg-primary: zenera-color($zenera-secondary, 900),
  bg-secondary: zenera-color($zenera-secondary, 800),
  bg-tertiary: zenera-color($zenera-secondary, 700),
  bg-overlay: rgba(0, 0, 0, 0.7),
  
  // Text Colors
  text-primary: zenera-color($zenera-secondary, 50),
  text-secondary: zenera-color($zenera-secondary, 300),
  text-tertiary: zenera-color($zenera-secondary, 400),
  text-inverse: zenera-color($zenera-secondary, 900),
  
  // Border Colors
  border-primary: zenera-color($zenera-secondary, 700),
  border-secondary: zenera-color($zenera-secondary, 600),
  
  // Component Specific
  card-bg: zenera-color($zenera-secondary, 800),
  card-border: zenera-color($zenera-secondary, 700),
  card-shadow: zenera-shadow(lg),
);

// === THEME MIXIN ===
@mixin zenera-apply-theme($theme-map) {
  @each $property, $value in $theme-map {
    --zenera-#{$property}: #{$value};
  }
}

// === BASE THEME CLASS ===
.zenera-theme {
  @include zenera-apply-theme($zenera-theme-base);
  
  // Apply font families
  @include zenera-font(sans);
  
  // Base transitions
  * {
    @include zenera-transition(all, 200, out);
  }
  
  // Respect user motion preferences
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// === DARK THEME CLASS ===
.zenera-theme.dark {
  @include zenera-apply-theme($zenera-theme-dark);
}

// === COMPONENT BASE STYLES ===

// Button Base
.zenera-btn {
  background-color: var(--zenera-primary);
  color: var(--zenera-text-inverse);
  border-radius: var(--zenera-button-radius);
  border: none;
  padding: zenera-spacing(2) zenera-spacing(4);
  font-weight: zenera-font-weight(medium);
  @include zenera-transition(all, 200, out);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: zenera-spacing(2);
  
  &:hover {
    background-color: var(--zenera-primary-hover);
    transform: translateY(-1px);
    @include zenera-shadow(md);
  }
  
  &:active {
    background-color: var(--zenera-primary-active);
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
  
  // Button Variants
  &.zenera-btn-secondary {
    background-color: var(--zenera-secondary);
    
    &:hover {
      background-color: var(--zenera-secondary-hover);
    }
    
    &:active {
      background-color: var(--zenera-secondary-active);
    }
  }
  
  &.zenera-btn-outline {
    background-color: transparent;
    color: var(--zenera-primary);
    border: 2px solid var(--zenera-primary);
    
    &:hover {
      background-color: var(--zenera-primary);
      color: var(--zenera-text-inverse);
    }
  }
  
  &.zenera-btn-ghost {
    background-color: transparent;
    color: var(--zenera-text-primary);
    
    &:hover {
      background-color: var(--zenera-bg-secondary);
    }
  }
  
  // Button Sizes
  &.zenera-btn-sm {
    padding: zenera-spacing(1-5) zenera-spacing(3);
    @include zenera-text(sm);
  }
  
  &.zenera-btn-lg {
    padding: zenera-spacing(3) zenera-spacing(6);
    @include zenera-text(lg);
  }
}

// Card Base
.zenera-card {
  background-color: var(--zenera-card-bg);
  border: 1px solid var(--zenera-card-border);
  border-radius: var(--zenera-card-radius);
  box-shadow: var(--zenera-card-shadow);
  padding: zenera-spacing(6);
  @include zenera-transition(all, 300, out);
  
  &:hover {
    @include zenera-shadow(lg);
    transform: translateY(-2px);
  }
  
  // Card Variants
  &.zenera-card-flat {
    box-shadow: none;
    border: 1px solid var(--zenera-border-primary);
  }
  
  &.zenera-card-elevated {
    @include zenera-shadow(md);
    
    &:hover {
      @include zenera-shadow(xl);
    }
  }
}

// Input Base
.zenera-input {
  background-color: var(--zenera-bg-primary);
  border: 2px solid var(--zenera-border-primary);
  border-radius: var(--zenera-input-radius);
  padding: zenera-spacing(3) zenera-spacing(4);
  color: var(--zenera-text-primary);
  @include zenera-text(base);
  @include zenera-transition(all, 200, out);
  width: 100%;
  
  &:focus {
    outline: none;
    border-color: var(--zenera-border-focus);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  &::placeholder {
    color: var(--zenera-text-tertiary);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: var(--zenera-bg-secondary);
  }
}

// Typography Base
.zenera-heading {
  @include zenera-font(display);
  font-weight: zenera-font-weight(bold);
  color: var(--zenera-text-primary);
  line-height: map-get($zenera-line-heights, tight);
  
  &.zenera-h1 {
    @include zenera-text(4xl, bold, tight);
  }
  
  &.zenera-h2 {
    @include zenera-text(3xl, bold, tight);
  }
  
  &.zenera-h3 {
    @include zenera-text(2xl, semibold, tight);
  }
  
  &.zenera-h4 {
    @include zenera-text(xl, semibold, tight);
  }
  
  &.zenera-h5 {
    @include zenera-text(lg, medium, tight);
  }
  
  &.zenera-h6 {
    @include zenera-text(base, medium, tight);
  }
}

.zenera-text {
  color: var(--zenera-text-primary);
  
  &.zenera-text-secondary {
    color: var(--zenera-text-secondary);
  }
  
  &.zenera-text-tertiary {
    color: var(--zenera-text-tertiary);
  }
  
  &.zenera-text-inverse {
    color: var(--zenera-text-inverse);
  }
}

// Link Base
.zenera-link {
  color: var(--zenera-primary);
  text-decoration: none;
  @include zenera-transition(all, 200, out);
  
  &:hover {
    color: var(--zenera-primary-hover);
    text-decoration: underline;
  }
  
  &:active {
    color: var(--zenera-primary-active);
  }
}
