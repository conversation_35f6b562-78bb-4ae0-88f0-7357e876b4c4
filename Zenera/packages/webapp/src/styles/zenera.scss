// Zenera Design System - Main SCSS Entry Point
// Import order is important for proper cascading

@use "sass:map";

// === FOUNDATIONS ===
@use 'variables';

// === ANIMATIONS ===
@use 'animations/keyframes';
@use 'animations/mixins';
@use 'animations/utilities';

// === THEMES ===
@use 'themes/base';
@use 'themes/variants';

// === GLOBAL STYLES ===

// Reset and base styles
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  
  @include variables.zenera-mobile-only {
    font-size: 14px;
  }
}

body {
  @include variables.zenera-font(sans);
  line-height: map.get(variables.$zenera-line-heights, normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  background-color: var(--zenera-bg-primary, #ffffff);
  color: var(--zenera-text-primary, #0f172a);
}

// === UTILITY CLASSES ===

// Spacing utilities
@each $name, $value in $zenera-spacing {
  .zenera-p-#{$name} { padding: $value; }
  .zenera-pt-#{$name} { padding-top: $value; }
  .zenera-pr-#{$name} { padding-right: $value; }
  .zenera-pb-#{$name} { padding-bottom: $value; }
  .zenera-pl-#{$name} { padding-left: $value; }
  .zenera-px-#{$name} { padding-left: $value; padding-right: $value; }
  .zenera-py-#{$name} { padding-top: $value; padding-bottom: $value; }
  
  .zenera-m-#{$name} { margin: $value; }
  .zenera-mt-#{$name} { margin-top: $value; }
  .zenera-mr-#{$name} { margin-right: $value; }
  .zenera-mb-#{$name} { margin-bottom: $value; }
  .zenera-ml-#{$name} { margin-left: $value; }
  .zenera-mx-#{$name} { margin-left: $value; margin-right: $value; }
  .zenera-my-#{$name} { margin-top: $value; margin-bottom: $value; }
}

// Text utilities
@each $name, $size in $zenera-font-sizes {
  .zenera-text-#{$name} {
    font-size: $size;
  }
}

@each $name, $weight in $zenera-font-weights {
  .zenera-font-#{$name} {
    font-weight: $weight;
  }
}

// Color utilities
.zenera-text-primary { color: var(--zenera-text-primary); }
.zenera-text-secondary { color: var(--zenera-text-secondary); }
.zenera-text-tertiary { color: var(--zenera-text-tertiary); }
.zenera-text-inverse { color: var(--zenera-text-inverse); }

.zenera-bg-primary { background-color: var(--zenera-bg-primary); }
.zenera-bg-secondary { background-color: var(--zenera-bg-secondary); }
.zenera-bg-tertiary { background-color: var(--zenera-bg-tertiary); }

// Border radius utilities
@each $name, $radius in $zenera-radius {
  .zenera-rounded-#{$name} {
    border-radius: $radius;
  }
}

// Shadow utilities
@each $name, $shadow in $zenera-shadows {
  .zenera-shadow-#{$name} {
    box-shadow: $shadow;
  }
}

// Display utilities
.zenera-flex { display: flex; }
.zenera-inline-flex { display: inline-flex; }
.zenera-grid { display: grid; }
.zenera-block { display: block; }
.zenera-inline-block { display: inline-block; }
.zenera-hidden { display: none; }

// Flex utilities
.zenera-flex-col { flex-direction: column; }
.zenera-flex-row { flex-direction: row; }
.zenera-flex-wrap { flex-wrap: wrap; }
.zenera-flex-nowrap { flex-wrap: nowrap; }

.zenera-justify-start { justify-content: flex-start; }
.zenera-justify-center { justify-content: center; }
.zenera-justify-end { justify-content: flex-end; }
.zenera-justify-between { justify-content: space-between; }
.zenera-justify-around { justify-content: space-around; }

.zenera-items-start { align-items: flex-start; }
.zenera-items-center { align-items: center; }
.zenera-items-end { align-items: flex-end; }
.zenera-items-stretch { align-items: stretch; }

// Gap utilities
@each $name, $value in $zenera-spacing {
  .zenera-gap-#{$name} { gap: $value; }
}

// Position utilities
.zenera-relative { position: relative; }
.zenera-absolute { position: absolute; }
.zenera-fixed { position: fixed; }
.zenera-sticky { position: sticky; }

// Z-index utilities
@each $name, $z in $zenera-z-index {
  .zenera-z-#{$name} { z-index: $z; }
}

// Width and height utilities
.zenera-w-full { width: 100%; }
.zenera-h-full { height: 100%; }
.zenera-w-auto { width: auto; }
.zenera-h-auto { height: auto; }

// Text alignment
.zenera-text-left { text-align: left; }
.zenera-text-center { text-align: center; }
.zenera-text-right { text-align: right; }

// Text truncation
.zenera-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.zenera-line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.zenera-line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.zenera-line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

// === COMPONENT STYLES ===

// Container
.zenera-container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 zenera-spacing(4);
  
  @include zenera-responsive(sm) {
    padding: 0 zenera-spacing(6);
  }
  
  @include zenera-responsive(lg) {
    padding: 0 zenera-spacing(8);
  }
}

// Grid system
.zenera-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.zenera-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.zenera-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.zenera-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.zenera-grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.zenera-grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

// Responsive grid
@include zenera-responsive(sm) {
  .zenera-sm-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .zenera-sm-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .zenera-sm-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .zenera-sm-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@include zenera-responsive(md) {
  .zenera-md-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .zenera-md-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .zenera-md-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .zenera-md-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .zenera-md-grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}

@include zenera-responsive(lg) {
  .zenera-lg-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .zenera-lg-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .zenera-lg-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .zenera-lg-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .zenera-lg-grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  .zenera-lg-grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }
}

// === ACCESSIBILITY ===

// Focus styles
.zenera-focus-visible {
  &:focus-visible {
    outline: 2px solid var(--zenera-primary);
    outline-offset: 2px;
  }
}

// Screen reader only
.zenera-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// === PRINT STYLES ===
@media print {
  .zenera-no-print {
    display: none !important;
  }
  
  .zenera-theme {
    --zenera-bg-primary: white;
    --zenera-text-primary: black;
    --zenera-text-secondary: #4a5568;
  }
  
  .zenera-card {
    box-shadow: none;
    border: 1px solid #e2e8f0;
  }
}
