// Zenera Design System - SCSS Variables
// Inspired by Medoo patterns with unique Zenera identity

@use "sass:map";

// === COLORS ===
// Primary - Zenera Blue (Professional & Trustworthy)
$zenera-primary: (
  50: #eff6ff,
  100: #dbeafe,
  200: #bfdbfe,
  300: #93c5fd,
  400: #60a5fa,
  500: #3b82f6,
  600: #2563eb,
  700: #1d4ed8,
  800: #1e40af,
  900: #1e3a8a,
  950: #172554,
);

// Secondary - Zenera Gray (Elegant & Modern)
$zenera-secondary: (
  50: #f8fafc,
  100: #f1f5f9,
  200: #e2e8f0,
  300: #cbd5e1,
  400: #94a3b8,
  500: #64748b,
  600: #475569,
  700: #334155,
  800: #1e293b,
  900: #0f172a,
  950: #020617,
);

// Accent - Zenera Gold (Premium & Luxury)
$zenera-accent: (
  50: #fffbeb,
  100: #fef3c7,
  200: #fde68a,
  300: #fcd34d,
  400: #fbbf24,
  500: #f59e0b,
  600: #d97706,
  700: #b45309,
  800: #92400e,
  900: #78350f,
  950: #451a03,
);

// Semantic Colors
$zenera-success: (
  50: #f0fdf4,
  500: #22c55e,
  600: #16a34a,
  700: #15803d,
);

$zenera-warning: (
  50: #fffbeb,
  500: #f59e0b,
  600: #d97706,
  700: #b45309,
);

$zenera-error: (
  50: #fef2f2,
  500: #ef4444,
  600: #dc2626,
  700: #b91c1c,
);

$zenera-info: (
  50: #eff6ff,
  500: #3b82f6,
  600: #2563eb,
  700: #1d4ed8,
);

// === TYPOGRAPHY ===
$zenera-fonts: (
  sans: ('Inter', system-ui, -apple-system, sans-serif),
  display: ('Poppins', 'Inter', system-ui, sans-serif),
  mono: ('JetBrains Mono', 'Fira Code', 'Consolas', monospace),
);

$zenera-font-sizes: (
  xs: 0.75rem,    // 12px
  sm: 0.875rem,   // 14px
  base: 1rem,     // 16px
  lg: 1.125rem,   // 18px
  xl: 1.25rem,    // 20px
  2xl: 1.5rem,    // 24px
  3xl: 1.875rem,  // 30px
  4xl: 2.25rem,   // 36px
  5xl: 3rem,      // 48px
  6xl: 3.75rem,   // 60px
);

$zenera-font-weights: (
  light: 300,
  normal: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
  extrabold: 800,
);

$zenera-line-heights: (
  tight: 1.25,
  snug: 1.375,
  normal: 1.5,
  relaxed: 1.625,
  loose: 2,
);

// === SPACING ===
$zenera-spacing: (
  0: 0,
  px: 1px,
  0-5: 0.125rem,  // 2px
  1: 0.25rem,     // 4px
  1-5: 0.375rem,  // 6px
  2: 0.5rem,      // 8px
  2-5: 0.625rem,  // 10px
  3: 0.75rem,     // 12px
  3-5: 0.875rem,  // 14px
  4: 1rem,        // 16px
  5: 1.25rem,     // 20px
  6: 1.5rem,      // 24px
  7: 1.75rem,     // 28px
  8: 2rem,        // 32px
  9: 2.25rem,     // 36px
  10: 2.5rem,     // 40px
  12: 3rem,       // 48px
  14: 3.5rem,     // 56px
  16: 4rem,       // 64px
  20: 5rem,       // 80px
  24: 6rem,       // 96px
  32: 8rem,       // 128px
);

// === BORDER RADIUS ===
$zenera-radius: (
  none: 0,
  sm: 0.125rem,   // 2px
  base: 0.25rem,  // 4px
  md: 0.375rem,   // 6px
  lg: 0.5rem,     // 8px
  xl: 0.75rem,    // 12px
  2xl: 1rem,      // 16px
  3xl: 1.5rem,    // 24px
  full: 9999px,
);

// === SHADOWS ===
$zenera-shadows: (
  xs: (0 1px 2px 0 rgb(0 0 0 / 0.05)),
  sm: (0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)),
  base: (0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)),
  md: (0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)),
  lg: (0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)),
  xl: (0 25px 50px -12px rgb(0 0 0 / 0.25)),
  2xl: (0 25px 50px -12px rgb(0 0 0 / 0.25)),
  inner: (inset 0 2px 4px 0 rgb(0 0 0 / 0.05)),
);

// === Z-INDEX ===
$zenera-z-index: (
  auto: auto,
  0: 0,
  10: 10,
  20: 20,
  30: 30,
  40: 40,
  50: 50,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal-backdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  toast: 1080,
);

// === ANIMATION DURATIONS ===
$zenera-durations: (
  75: 75ms,
  100: 100ms,
  150: 150ms,
  200: 200ms,
  300: 300ms,
  500: 500ms,
  700: 700ms,
  1000: 1000ms,
);

// === ANIMATION EASING ===
$zenera-easing: (
  linear: linear,
  in: cubic-bezier(0.4, 0, 1, 1),
  out: cubic-bezier(0, 0, 0.2, 1),
  in-out: cubic-bezier(0.4, 0, 0.2, 1),
  sharp: cubic-bezier(0.4, 0, 0.6, 1),
  bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55),
);

// === BREAKPOINTS ===
$zenera-breakpoints: (
  sm: 640px,
  md: 768px,
  lg: 1024px,
  xl: 1280px,
  2xl: 1536px,
);

// === HELPER FUNCTIONS ===
@function zenera-color($palette, $shade: 500) {
  @return map.get($palette, $shade);
}

@function zenera-spacing($size) {
  @return map.get($zenera-spacing, $size);
}

@function zenera-radius($size) {
  @return map.get($zenera-radius, $size);
}

@function zenera-shadow($size) {
  @return map.get($zenera-shadows, $size);
}

@function zenera-font-size($size) {
  @return map.get($zenera-font-sizes, $size);
}

@function zenera-font-weight($weight) {
  @return map.get($zenera-font-weights, $weight);
}

@function zenera-duration($duration) {
  @return map.get($zenera-durations, $duration);
}

@function zenera-easing($easing) {
  @return map.get($zenera-easing, $easing);
}

@function zenera-z($layer) {
  @return map.get($zenera-z-index, $layer);
}

// === MIXINS ===
@mixin zenera-font($family: sans) {
  font-family: map.get($zenera-fonts, $family);
}

@mixin zenera-text($size: base, $weight: normal, $line-height: normal) {
  font-size: zenera-font-size($size);
  font-weight: zenera-font-weight($weight);
  line-height: map.get($zenera-line-heights, $line-height);
}

@mixin zenera-shadow($size: base) {
  box-shadow: zenera-shadow($size);
}

@mixin zenera-transition($property: all, $duration: 200, $easing: out) {
  transition: $property zenera-duration($duration) zenera-easing($easing);
}

// === RESPONSIVE MIXINS ===
@mixin zenera-responsive($breakpoint) {
  @media (min-width: map.get($zenera-breakpoints, $breakpoint)) {
    @content;
  }
}

@mixin zenera-mobile-only {
  @media (max-width: map.get($zenera-breakpoints, md) - 1px) {
    @content;
  }
}

@mixin zenera-tablet-only {
  @media (min-width: map.get($zenera-breakpoints, md)) and (max-width: map.get($zenera-breakpoints, lg) - 1px) {
    @content;
  }
}

@mixin zenera-desktop-only {
  @media (min-width: map.get($zenera-breakpoints, lg)) {
    @content;
  }
}
