"use client";

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { User } from '@zenera/sharing/types';

// Define local types
type Role = 'customer' | 'seller' | 'admin' | 'super_admin';
type UserStatus = 'active' | 'inactive' | 'suspended' | 'pending_verification';

interface UserState {
  // State
  currentUser: User | null;
  users: User[];
  isLoading: boolean;
  error: string | null;
  
  // User management actions
  setCurrentUser: (user: User | null) => void;
  updateCurrentUser: (updates: Partial<User>) => void;
  setUsers: (users: User[]) => void;
  addUser: (user: User) => void;
  updateUser: (userId: string, updates: Partial<User>) => void;
  removeUser: (userId: string) => void;
  
  // User operations (to be implemented when needed)
  fetchUser: (userId: string) => Promise<void>;
  fetchUsers: (filters?: any) => Promise<void>;
  createUser: (userData: Partial<User>) => Promise<void>;
  updateUserProfile: (updates: Partial<User>) => Promise<void>;
  deleteUser: (userId: string) => Promise<void>;
  
  // User role management
  updateUserRoles: (userId: string, roles: Role[]) => Promise<void>;
  updateUserStatus: (userId: string, status: UserStatus) => Promise<void>;
  
  // Utility actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  currentUser: null,
  users: [],
  isLoading: false,
  error: null,
};

export const useUserStore = create<UserState>()(
  persist(
    immer((set) => ({
      ...initialState,
      
      // Basic state management
      setCurrentUser: (user) => {
        set((state) => {
          state.currentUser = user;
        });
      },
      
      updateCurrentUser: (updates) => {
        set((state) => {
          if (state.currentUser) {
            Object.assign(state.currentUser, updates);
          }
        });
      },
      
      setUsers: (users) => {
        set((state) => {
          state.users = users;
        });
      },
      
      addUser: (user) => {
        set((state) => {
          state.users.push(user);
        });
      },
      
      updateUser: (userId, updates) => {
        set((state) => {
          const userIndex = state.users.findIndex(u => (u as any)._id === userId || u.id === userId);
          if (userIndex !== -1) {
            Object.assign(state.users[userIndex], updates);
          }
        });
      },
      
      removeUser: (userId) => {
        set((state) => {
          state.users = state.users.filter(u => (u as any)._id !== userId && u.id !== userId);
        });
      },
      
      // Async operations (placeholder implementations)
      fetchUser: async (userId) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('fetchUser:', userId);
          // const user = await userApi.getUser(userId);
          // set((state) => {
          //   const existingIndex = state.users.findIndex(u => u._id === userId || u.id === userId);
          //   if (existingIndex !== -1) {
          //     state.users[existingIndex] = user;
          //   } else {
          //     state.users.push(user);
          //   }
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch user';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      fetchUsers: async (filters) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('fetchUsers:', filters);
          // const users = await userApi.getUsers(filters);
          // set((state) => {
          //   state.users = users;
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch users';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      createUser: async (userData) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('createUser:', userData);
          // const newUser = await userApi.createUser(userData);
          // set((state) => {
          //   state.users.push(newUser);
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to create user';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      updateUserProfile: async (updates) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('updateUserProfile:', updates);
          // const updatedUser = await userApi.updateProfile(updates);
          // set((state) => {
          //   state.currentUser = updatedUser;
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update profile';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      deleteUser: async (userId) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('deleteUser:', userId);
          // await userApi.deleteUser(userId);
          // set((state) => {
          //   state.users = state.users.filter(u => u._id !== userId && u.id !== userId);
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to delete user';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      updateUserRoles: async (userId, roles) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('updateUserRoles:', userId, roles);
          // await userApi.updateRoles(userId, roles);
          // get().updateUser(userId, { roles });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update user roles';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      updateUserStatus: async (userId, status) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('updateUserStatus:', userId, status);
          // await userApi.updateStatus(userId, status);
          // get().updateUser(userId, { status });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update user status';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      // Utility actions
      setLoading: (loading) => {
        set((state) => {
          state.isLoading = loading;
        });
      },
      
      setError: (error) => {
        set((state) => {
          state.error = error;
        });
      },
      
      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },
      
      reset: () => {
        set((state) => {
          Object.assign(state, initialState);
        });
      },
    })),
    {
      name: 'zenera-user-store',
      partialize: (state) => ({
        currentUser: state.currentUser,
      }),
    }
  )
);
