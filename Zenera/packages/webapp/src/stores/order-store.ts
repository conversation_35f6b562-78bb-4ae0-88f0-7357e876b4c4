"use client";

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { Order, OrderItem } from '@zenera/sharing/types';

// Define local types
type OrderStatus = 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';

interface ShippingAddress {
  id?: string;
  recipient_name: string;
  phone: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  is_default?: boolean;
}

interface PaymentMethod {
  id?: string;
  type: 'credit_card' | 'debit_card' | 'bank_transfer' | 'e_wallet' | 'cash_on_delivery';
  provider?: string;
  account_number?: string;
  is_default?: boolean;
}

interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

interface OrderFilters {
  status?: OrderStatus;
  user_id?: string;
  date_from?: string;
  date_to?: string;
  payment_method?: PaymentMethod;
}

interface OrderState {
  // State
  orders: Order[];
  currentOrder: Order | null;
  isLoading: boolean;
  error: string | null;
  pagination: PaginationMeta | null;
  filters: OrderFilters;
  
  // Order management actions
  setOrders: (orders: Order[]) => void;
  addOrder: (order: Order) => void;
  updateOrder: (orderId: string, updates: Partial<Order>) => void;
  removeOrder: (orderId: string) => void;
  setCurrentOrder: (order: Order | null) => void;
  
  // Order operations (to be implemented when needed)
  fetchOrders: (filters?: OrderFilters, page?: number, limit?: number) => Promise<void>;
  fetchOrder: (orderId: string) => Promise<void>;
  createOrder: (orderData: {
    items: OrderItem[];
    shipping_address: ShippingAddress;
    payment_method: PaymentMethod;
    order_notes?: string;
  }) => Promise<void>;
  updateOrderStatus: (orderId: string, status: OrderStatus, comment?: string) => Promise<void>;
  cancelOrder: (orderId: string, reason?: string) => Promise<void>;
  
  // Order status management
  getOrdersByStatus: (status: OrderStatus) => Order[];
  getOrderHistory: (orderId: string) => any[];
  
  // Filter management
  setFilters: (filters: OrderFilters) => void;
  clearFilters: () => void;
  
  // Utility actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  setPagination: (pagination: PaginationMeta) => void;
  reset: () => void;
}

const initialState = {
  orders: [],
  currentOrder: null,
  isLoading: false,
  error: null,
  pagination: null,
  filters: {},
};

export const useOrderStore = create<OrderState>()(
  persist(
    immer((set, get) => ({
      ...initialState,
      
      // Basic state management
      setOrders: (orders) => {
        set((state) => {
          state.orders = orders;
        });
      },
      
      addOrder: (order) => {
        set((state) => {
          state.orders.unshift(order); // Add to beginning for newest first
        });
      },
      
      updateOrder: (orderId, updates) => {
        set((state) => {
          const orderIndex = state.orders.findIndex(o => (o as any)._id === orderId || o.id === orderId);
          if (orderIndex !== -1) {
            Object.assign(state.orders[orderIndex], updates);
          }
          
          // Update current order if it's the same
          if (state.currentOrder && ((state.currentOrder as any)._id === orderId || state.currentOrder.id === orderId)) {
            Object.assign(state.currentOrder, updates);
          }
        });
      },
      
      removeOrder: (orderId) => {
        set((state) => {
          state.orders = state.orders.filter(o => (o as any)._id !== orderId && o.id !== orderId);
          
          // Clear current order if it's the same
          if (state.currentOrder && ((state.currentOrder as any)._id === orderId || state.currentOrder.id === orderId)) {
            state.currentOrder = null;
          }
        });
      },
      
      setCurrentOrder: (order) => {
        set((state) => {
          state.currentOrder = order;
        });
      },
      
      // Async operations (placeholder implementations)
      fetchOrders: async (filters, page = 1, limit = 10) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('fetchOrders:', filters, page, limit);
          // const response = await orderApi.getOrders(filters, page, limit);
          // set((state) => {
          //   state.orders = response.data;
          //   state.pagination = response.pagination;
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch orders';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      fetchOrder: async (orderId) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('fetchOrder:', orderId);
          // const order = await orderApi.getOrder(orderId);
          // set((state) => {
          //   state.currentOrder = order;
          //   
          //   // Update in orders list if exists
          //   const orderIndex = state.orders.findIndex(o => o._id === orderId || o.id === orderId);
          //   if (orderIndex !== -1) {
          //     state.orders[orderIndex] = order;
          //   }
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch order';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      createOrder: async (orderData) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('createOrder:', orderData);
          // const newOrder = await orderApi.createOrder(orderData);
          // set((state) => {
          //   state.orders.unshift(newOrder);
          //   state.currentOrder = newOrder;
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to create order';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      updateOrderStatus: async (orderId, status, comment) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('updateOrderStatus:', orderId, status, comment);
          // await orderApi.updateOrderStatus(orderId, status, comment);
          // 
          // // Update local state
          // const statusHistoryItem = {
          //   status,
          //   timestamp: new Date(),
          //   comment,
          //   updated_by: 'current_user_id' // TODO: Get from auth store
          // };
          // 
          // get().updateOrder(orderId, {
          //   order_status: status,
          //   status_history: [...(currentOrder?.status_history || []), statusHistoryItem]
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update order status';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      cancelOrder: async (orderId, reason) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('cancelOrder:', orderId, reason);
          // await orderApi.cancelOrder(orderId, reason);
          // get().updateOrderStatus(orderId, OrderStatus.CANCELLED, reason);
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to cancel order';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      // Order status management
      getOrdersByStatus: (status) => {
        return get().orders.filter(order => (order as any).order_status === status);
      },
      
      getOrderHistory: (orderId) => {
        const order = get().orders.find(o => (o as any)._id === orderId || o.id === orderId);
        return (order as any)?.status_history || [];
      },
      
      // Filter management
      setFilters: (filters) => {
        set((state) => {
          state.filters = filters;
        });
      },
      
      clearFilters: () => {
        set((state) => {
          state.filters = {};
        });
      },
      
      // Utility actions
      setLoading: (loading) => {
        set((state) => {
          state.isLoading = loading;
        });
      },
      
      setError: (error) => {
        set((state) => {
          state.error = error;
        });
      },
      
      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },
      
      setPagination: (pagination) => {
        set((state) => {
          state.pagination = pagination;
        });
      },
      
      reset: () => {
        set((state) => {
          Object.assign(state, initialState);
        });
      },
    })),
    {
      name: 'zenera-order-store',
      partialize: (state) => ({
        // Don't persist orders for security reasons
        filters: state.filters,
      }),
    }
  )
);
