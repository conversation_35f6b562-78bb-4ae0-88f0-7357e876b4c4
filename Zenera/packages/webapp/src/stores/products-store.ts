"use client";

import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import type { Product, Category, SearchFilters } from '@zenera/sharing/types';

interface ProductsState {
  // State
  products: Product[];
  categories: Category[];
  featuredProducts: Product[];
  searchResults: Product[];
  filters: SearchFilters;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  
  // Actions
  setProducts: (products: Product[]) => void;
  addProduct: (product: Product) => void;
  updateProduct: (productId: string, updates: Partial<Product>) => void;
  removeProduct: (productId: string) => void;
  
  // Categories
  setCategories: (categories: Category[]) => void;
  addCategory: (category: Category) => void;
  
  // Featured products
  setFeaturedProducts: (products: Product[]) => void;
  
  // Search & Filters
  setSearchResults: (products: Product[]) => void;
  setFilters: (filters: SearchFilters) => void;
  updateFilters: (updates: Partial<SearchFilters>) => void;
  clearFilters: () => void;
  
  // Loading & Error
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Pagination
  setPagination: (pagination: Partial<ProductsState['pagination']>) => void;
  
  // API Actions (placeholder for now)
  fetchProducts: (filters?: SearchFilters) => Promise<void>;
  fetchCategories: () => Promise<void>;
  fetchFeaturedProducts: () => Promise<void>;
  searchProducts: (query: string, filters?: SearchFilters) => Promise<void>;
  
  // Getters
  getProductById: (id: string) => Product | undefined;
  getProductsByCategory: (categoryId: string) => Product[];
  getCategoryById: (id: string) => Category | undefined;
}

const initialFilters: SearchFilters = {
  category_ids: [],
  price_min: undefined,
  price_max: undefined,
  brand: undefined,
  rating_min: undefined,
  in_stock: undefined,
  tags: [],
  seller_id: undefined,
  location: undefined,
  sort_by: 'relevance',
};

const initialPagination = {
  page: 1,
  limit: 12,
  total: 0,
  totalPages: 0,
};

export const useProductsStore = create<ProductsState>()(
  immer((set, get) => ({
    // Initial state
    products: [],
    categories: [],
    featuredProducts: [],
    searchResults: [],
    filters: initialFilters,
    isLoading: false,
    error: null,
    pagination: initialPagination,
    
    // Basic state management
    setProducts: (products) => {
      set((state) => {
        state.products = products;
      });
    },
    
    addProduct: (product) => {
      set((state) => {
        state.products.push(product);
      });
    },
    
    updateProduct: (productId, updates) => {
      set((state) => {
        const index = state.products.findIndex(p => p._id === productId || p.id === productId);
        if (index !== -1) {
          Object.assign(state.products[index], updates);
        }
      });
    },
    
    removeProduct: (productId) => {
      set((state) => {
        state.products = state.products.filter(p => p._id !== productId && p.id !== productId);
      });
    },
    
    // Categories
    setCategories: (categories) => {
      set((state) => {
        state.categories = categories;
      });
    },
    
    addCategory: (category) => {
      set((state) => {
        state.categories.push(category);
      });
    },
    
    // Featured products
    setFeaturedProducts: (products) => {
      set((state) => {
        state.featuredProducts = products;
      });
    },
    
    // Search & Filters
    setSearchResults: (products) => {
      set((state) => {
        state.searchResults = products;
      });
    },
    
    setFilters: (filters) => {
      set((state) => {
        state.filters = filters;
      });
    },
    
    updateFilters: (updates) => {
      set((state) => {
        Object.assign(state.filters, updates);
      });
    },
    
    clearFilters: () => {
      set((state) => {
        state.filters = { ...initialFilters };
      });
    },
    
    // Loading & Error
    setLoading: (loading) => {
      set((state) => {
        state.isLoading = loading;
      });
    },
    
    setError: (error) => {
      set((state) => {
        state.error = error;
      });
    },
    
    clearError: () => {
      set((state) => {
        state.error = null;
      });
    },
    
    // Pagination
    setPagination: (pagination) => {
      set((state) => {
        Object.assign(state.pagination, pagination);
      });
    },
    
    // API Actions (placeholder implementations)
    fetchProducts: async (filters) => {
      set((state) => {
        state.isLoading = true;
        state.error = null;
      });
      
      try {
        // TODO: Replace with actual API call
        const queryParams = new URLSearchParams();
        if (filters) {
          Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              queryParams.append(key, String(value));
            }
          });
        }
        
        const response = await fetch(`/api/products?${queryParams}`);
        if (!response.ok) {
          throw new Error('Failed to fetch products');
        }
        
        const data = await response.json();
        
        set((state) => {
          state.products = data.products || [];
          state.pagination = {
            page: data.page || 1,
            limit: data.limit || 12,
            total: data.total || 0,
            totalPages: data.totalPages || 0,
          };
          state.isLoading = false;
        });
      } catch (error) {
        set((state) => {
          state.isLoading = false;
          state.error = error instanceof Error ? error.message : 'Failed to fetch products';
        });
      }
    },
    
    fetchCategories: async () => {
      try {
        // TODO: Replace with actual API call
        const response = await fetch('/api/categories');
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }
        
        const categories = await response.json();
        get().setCategories(categories);
      } catch (error) {
        get().setError(error instanceof Error ? error.message : 'Failed to fetch categories');
      }
    },
    
    fetchFeaturedProducts: async () => {
      try {
        // TODO: Replace with actual API call
        const response = await fetch('/api/products/featured');
        if (!response.ok) {
          throw new Error('Failed to fetch featured products');
        }
        
        const products = await response.json();
        get().setFeaturedProducts(products);
      } catch (error) {
        get().setError(error instanceof Error ? error.message : 'Failed to fetch featured products');
      }
    },
    
    searchProducts: async (query, filters) => {
      set((state) => {
        state.isLoading = true;
        state.error = null;
      });
      
      try {
        // TODO: Replace with actual API call
        const searchParams = new URLSearchParams({ query });
        if (filters) {
          Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              searchParams.append(key, String(value));
            }
          });
        }
        
        const response = await fetch(`/api/products/search?${searchParams}`);
        if (!response.ok) {
          throw new Error('Search failed');
        }
        
        const data = await response.json();
        
        set((state) => {
          state.searchResults = data.products || [];
          state.pagination = {
            page: data.page || 1,
            limit: data.limit || 12,
            total: data.total || 0,
            totalPages: data.totalPages || 0,
          };
          state.isLoading = false;
        });
      } catch (error) {
        set((state) => {
          state.isLoading = false;
          state.error = error instanceof Error ? error.message : 'Search failed';
        });
      }
    },
    
    // Getters
    getProductById: (id) => {
      const { products } = get();
      return products.find(p => p._id === id || p.id === id);
    },
    
    getProductsByCategory: (categoryId) => {
      const { products } = get();
      return products.filter(p => p.category_id === categoryId);
    },
    
    getCategoryById: (id) => {
      const { categories } = get();
      return categories.find(c => c._id === id || c.id === id);
    },
  }))
);
