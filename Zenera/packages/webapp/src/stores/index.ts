// Centralized export for all Zustand stores

export { useAuthStore } from './auth-store';
export { useUserStore } from './user-store';
export { useProductStore } from './product-store';
export { useProductsStore } from './products-store';
export { useOrderStore } from './order-store';
export { useCartStore } from './cart-store';
export { useNotificationStore } from './notification-store';

// Re-export types for convenience
export type { User } from '@zenera/sharing/types';
export type { Product } from '@zenera/sharing/types';
