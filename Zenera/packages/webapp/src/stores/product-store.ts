"use client";

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { Product } from '@zenera/sharing/types';

// Define local types
interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parent_id?: string;
  image?: string;
  is_active?: boolean;
}

interface ProductVariant {
  id: string;
  name: string;
  price: number;
  stock: number;
  attributes: Record<string, string>;
}

interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

interface ProductFilters {
  category_id?: string;
  brand?: string;
  min_price?: number;
  max_price?: number;
  tags?: string[];
  search?: string;
  is_active?: boolean;
}

interface ProductState {
  // State
  products: Product[];
  categories: Category[];
  currentProduct: Product | null;
  productVariants: ProductVariant[];
  isLoading: boolean;
  error: string | null;
  pagination: PaginationMeta | null;
  filters: ProductFilters;
  
  // Product management actions
  setProducts: (products: Product[]) => void;
  addProduct: (product: Product) => void;
  updateProduct: (productId: string, updates: Partial<Product>) => void;
  removeProduct: (productId: string) => void;
  setCurrentProduct: (product: Product | null) => void;
  
  // Category management
  setCategories: (categories: Category[]) => void;
  addCategory: (category: Category) => void;
  updateCategory: (categoryId: string, updates: Partial<Category>) => void;
  removeCategory: (categoryId: string) => void;
  
  // Product variants
  setProductVariants: (variants: ProductVariant[]) => void;
  addProductVariant: (variant: ProductVariant) => void;
  updateProductVariant: (variantId: string, updates: Partial<ProductVariant>) => void;
  removeProductVariant: (variantId: string) => void;
  
  // Async operations (to be implemented when needed)
  fetchProducts: (filters?: ProductFilters, page?: number, limit?: number) => Promise<void>;
  fetchProduct: (productId: string) => Promise<void>;
  fetchCategories: () => Promise<void>;
  fetchProductVariants: (productId: string) => Promise<void>;
  searchProducts: (query: string) => Promise<void>;
  
  // CRUD operations
  createProduct: (productData: Partial<Product>) => Promise<void>;
  updateProductData: (productId: string, updates: Partial<Product>) => Promise<void>;
  deleteProduct: (productId: string) => Promise<void>;
  
  // Filter management
  setFilters: (filters: ProductFilters) => void;
  clearFilters: () => void;
  
  // Utility actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  setPagination: (pagination: PaginationMeta) => void;
  reset: () => void;
}

const initialState = {
  products: [],
  categories: [],
  currentProduct: null,
  productVariants: [],
  isLoading: false,
  error: null,
  pagination: null,
  filters: {},
};

export const useProductStore = create<ProductState>()(
  persist(
    immer((set, get) => ({
      ...initialState,
      
      // Basic state management
      setProducts: (products) => {
        set((state) => {
          state.products = products;
        });
      },
      
      addProduct: (product) => {
        set((state) => {
          state.products.push(product);
        });
      },
      
      updateProduct: (productId, updates) => {
        set((state) => {
          const productIndex = state.products.findIndex(p => (p as any)._id === productId || p.id === productId);
          if (productIndex !== -1) {
            Object.assign(state.products[productIndex], updates);
          }
        });
      },
      
      removeProduct: (productId) => {
        set((state) => {
          state.products = state.products.filter(p => (p as any)._id !== productId && p.id !== productId);
        });
      },
      
      setCurrentProduct: (product) => {
        set((state) => {
          state.currentProduct = product;
        });
      },
      
      // Category management
      setCategories: (categories) => {
        set((state) => {
          state.categories = categories;
        });
      },
      
      addCategory: (category) => {
        set((state) => {
          state.categories.push(category);
        });
      },
      
      updateCategory: (categoryId, updates) => {
        set((state) => {
          const categoryIndex = state.categories.findIndex(c => (c as any)._id === categoryId || c.id === categoryId);
          if (categoryIndex !== -1) {
            Object.assign(state.categories[categoryIndex], updates);
          }
        });
      },
      
      removeCategory: (categoryId) => {
        set((state) => {
          state.categories = state.categories.filter(c => (c as any)._id !== categoryId && c.id !== categoryId);
        });
      },
      
      // Product variants
      setProductVariants: (variants) => {
        set((state) => {
          state.productVariants = variants;
        });
      },
      
      addProductVariant: (variant) => {
        set((state) => {
          state.productVariants.push(variant);
        });
      },
      
      updateProductVariant: (variantId, updates) => {
        set((state) => {
          const variantIndex = state.productVariants.findIndex(v => (v as any)._id === variantId);
          if (variantIndex !== -1) {
            Object.assign(state.productVariants[variantIndex], updates);
          }
        });
      },
      
      removeProductVariant: (variantId) => {
        set((state) => {
          state.productVariants = state.productVariants.filter(v => (v as any)._id !== variantId);
        });
      },
      
      // Async operations (placeholder implementations)
      fetchProducts: async (filters, page = 1, limit = 10) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('fetchProducts:', filters, page, limit);
          // const response = await productApi.getProducts(filters, page, limit);
          // set((state) => {
          //   state.products = response.data;
          //   state.pagination = response.pagination;
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch products';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      fetchProduct: async (productId) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('fetchProduct:', productId);
          // const product = await productApi.getProduct(productId);
          // set((state) => {
          //   state.currentProduct = product;
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch product';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      fetchCategories: async () => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('fetchCategories');
          // const categories = await categoryApi.getCategories();
          // set((state) => {
          //   state.categories = categories;
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch categories';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      fetchProductVariants: async (productId) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('fetchProductVariants:', productId);
          // const variants = await productApi.getProductVariants(productId);
          // set((state) => {
          //   state.productVariants = variants;
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch product variants';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      searchProducts: async (query) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('searchProducts:', query);
          // const products = await productApi.searchProducts(query);
          // set((state) => {
          //   state.products = products;
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to search products';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      createProduct: async (productData) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('createProduct:', productData);
          // const newProduct = await productApi.createProduct(productData);
          // set((state) => {
          //   state.products.push(newProduct);
          // });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to create product';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      updateProductData: async (productId, updates) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('updateProductData:', productId, updates);
          // const updatedProduct = await productApi.updateProduct(productId, updates);
          // get().updateProduct(productId, updatedProduct);
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update product';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      deleteProduct: async (productId) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });
        
        try {
          // TODO: Implement API call
          console.log('deleteProduct:', productId);
          // await productApi.deleteProduct(productId);
          // get().removeProduct(productId);
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to delete product';
          });
        } finally {
          set((state) => {
            state.isLoading = false;
          });
        }
      },
      
      // Filter management
      setFilters: (filters) => {
        set((state) => {
          state.filters = filters;
        });
      },
      
      clearFilters: () => {
        set((state) => {
          state.filters = {};
        });
      },
      
      // Utility actions
      setLoading: (loading) => {
        set((state) => {
          state.isLoading = loading;
        });
      },
      
      setError: (error) => {
        set((state) => {
          state.error = error;
        });
      },
      
      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },
      
      setPagination: (pagination) => {
        set((state) => {
          state.pagination = pagination;
        });
      },
      
      reset: () => {
        set((state) => {
          Object.assign(state, initialState);
        });
      },
    })),
    {
      name: 'zenera-product-store',
      partialize: (state) => ({
        categories: state.categories,
        filters: state.filters,
      }),
    }
  )
);
