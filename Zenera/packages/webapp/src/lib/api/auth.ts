/**
 * Authentication API Service
 */

import { apiClient, API_ENDPOINTS } from '../api-client';

// Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  username?: string;
  phone_number?: string;
  role?: 'customer' | 'seller' | 'admin' | 'super_admin';
}

export interface AuthResponse {
  user: {
    id: string;
    email: string;
    username?: string;
    first_name: string;
    last_name: string;
    roles: string[];
    permissions?: string[];
    customer_info?: any;
    seller_info?: any;
    admin_info?: any;
  };
  access_token: string;
  refresh_token?: string;
}

export interface User {
  id: string;
  email: string;
  username?: string;
  first_name: string;
  last_name: string;
  roles: string[];
  permissions?: string[];
  customer_info?: any;
  seller_info?: any;
  admin_info?: any;
}

// Auth API Service
export const authApi = {
  /**
   * Login user
   */
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>(
      API_ENDPOINTS.AUTH.LOGIN,
      credentials
    );
    
    // Store auth token
    if (response.access_token) {
      apiClient.setAuthToken(response.access_token);
    }
    
    return response;
  },

  /**
   * Register new user
   */
  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>(
      API_ENDPOINTS.AUTH.REGISTER,
      userData
    );
    
    // Store auth token
    if (response.access_token) {
      apiClient.setAuthToken(response.access_token);
    }
    
    return response;
  },

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      await apiClient.post(API_ENDPOINTS.AUTH.LOGOUT);
    } finally {
      // Always clear token, even if API call fails
      apiClient.clearAuthToken();
    }
  },

  /**
   * Get current user profile
   */
  async getProfile(): Promise<User> {
    return apiClient.get<User>(API_ENDPOINTS.AUTH.PROFILE);
  },

  /**
   * Refresh access token
   */
  async refreshToken(): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>(
      API_ENDPOINTS.AUTH.REFRESH
    );
    
    // Update stored token
    if (response.access_token) {
      apiClient.setAuthToken(response.access_token);
    }
    
    return response;
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!apiClient.getAuthToken();
  },

  /**
   * Get stored auth token
   */
  getToken(): string | null {
    return apiClient.getAuthToken();
  },
};
