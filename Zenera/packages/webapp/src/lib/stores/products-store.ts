/**
 * Products Store
 * Adapted từ Medoo products management patterns cho Zenera E-commerce
 */

import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { api } from '@/lib/api';

// Types
export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription?: string;
  price: number;
  originalPrice?: number;
  discountPercentage?: number;
  sku: string;
  stock: number;
  images: string[];
  category: {
    id: string;
    name: string;
    slug: string;
  };
  seller: {
    id: string;
    name: string;
    avatar?: string;
    rating?: number;
  };
  attributes?: Record<string, string>;
  variants?: ProductVariant[];
  rating: number;
  reviewCount: number;
  tags: string[];
  isActive: boolean;
  isFeatured: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ProductVariant {
  id: string;
  name: string;
  sku: string;
  price: number;
  stock: number;
  attributes: Record<string, string>;
  images?: string[];
}

export interface ProductFilters {
  category?: string;
  seller?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  featured?: boolean;
  rating?: number;
  tags?: string[];
  search?: string;
}

export interface ProductSort {
  field: 'name' | 'price' | 'rating' | 'createdAt' | 'popularity';
  direction: 'asc' | 'desc';
}

export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface ProductsStore {
  // State
  products: Product[];
  featuredProducts: Product[];
  currentProduct: Product | null;
  categories: Array<{ id: string; name: string; slug: string; count: number }>;
  
  // Loading states
  isLoading: boolean;
  isFeaturedLoading: boolean;
  isProductLoading: boolean;
  
  // Error states
  error: string | null;
  productError: string | null;
  
  // Filters & pagination
  filters: ProductFilters;
  sort: ProductSort;
  pagination: Pagination;
  
  // Search
  searchQuery: string;
  searchResults: Product[];
  isSearching: boolean;
  
  // Actions - Product fetching
  fetchProducts: (params?: { filters?: ProductFilters; sort?: ProductSort; page?: number; limit?: number }) => Promise<void>;
  fetchFeaturedProducts: (limit?: number) => Promise<void>;
  fetchProductById: (id: string) => Promise<Product | null>;
  fetchProductBySlug: (slug: string) => Promise<Product | null>;
  
  // Actions - Search
  searchProducts: (query: string, filters?: ProductFilters) => Promise<void>;
  clearSearch: () => void;
  
  // Actions - Filters & sorting
  setFilters: (filters: Partial<ProductFilters>) => void;
  clearFilters: () => void;
  setSort: (sort: ProductSort) => void;
  setPagination: (page: number, limit?: number) => void;
  
  // Actions - Categories
  fetchCategories: () => Promise<void>;
  
  // Actions - Product management (for sellers)
  createProduct: (productData: Partial<Product>) => Promise<Product>;
  updateProduct: (id: string, productData: Partial<Product>) => Promise<Product>;
  deleteProduct: (id: string) => Promise<void>;
  
  // Utilities
  getProductById: (id: string) => Product | undefined;
  getProductsByCategory: (categoryId: string) => Product[];
  getProductsBySeller: (sellerId: string) => Product[];
  getRelatedProducts: (productId: string, limit?: number) => Product[];
  
  // Cache management
  invalidateCache: () => void;
  refreshProducts: () => Promise<void>;
  
  // Reset
  reset: () => void;
}

const initialPagination: Pagination = {
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0,
};

const initialSort: ProductSort = {
  field: 'createdAt',
  direction: 'desc',
};

/**
 * Products Store Implementation
 * Adapted từ Medoo products patterns với Zustand
 */
export const useProductsStore = create<ProductsStore>()(
  immer((set, get) => ({
    // Initial state
    products: [],
    featuredProducts: [],
    currentProduct: null,
    categories: [],
    
    isLoading: false,
    isFeaturedLoading: false,
    isProductLoading: false,
    
    error: null,
    productError: null,
    
    filters: {},
    sort: initialSort,
    pagination: initialPagination,
    
    searchQuery: '',
    searchResults: [],
    isSearching: false,
    
    // Fetch products with filters and pagination
    fetchProducts: async (params = {}) => {
      const { filters = {}, sort = get().sort, page = 1, limit = 20 } = params;
      
      set((state) => {
        state.isLoading = true;
        state.error = null;
        state.filters = { ...state.filters, ...filters };
        state.sort = sort;
        state.pagination.page = page;
        state.pagination.limit = limit;
      });

      try {
        const queryParams = {
          ...get().filters,
          sort: `${sort.field}:${sort.direction}`,
          page,
          limit,
        };

        const response = await api.products.getProducts(queryParams);
        
        set((state) => {
          state.products = response.products as any;
          state.pagination = response.pagination ? {
            page: response.pagination.page,
            limit: response.pagination.limit,
            total: response.pagination.total,
            totalPages: response.pagination.totalPages,
          } : {
            page: 1,
            limit: 20,
            total: response.products.length,
            totalPages: 1,
          };
        });
      } catch (error: any) {
        set((state) => {
          state.error = error.message || 'Failed to fetch products';
        });
      } finally {
        set((state) => {
          state.isLoading = false;
        });
      }
    },

    // Fetch featured products
    fetchFeaturedProducts: async (limit = 10) => {
      set((state) => {
        state.isFeaturedLoading = true;
      });

      try {
        const response = await api.products.getFeaturedProducts(limit);
        
        set((state) => {
          state.featuredProducts = response as any;
        });
      } catch (error: any) {
        console.error('Failed to fetch featured products:', error);
      } finally {
        set((state) => {
          state.isFeaturedLoading = false;
        });
      }
    },

    // Fetch single product by ID
    fetchProductById: async (id) => {
      set((state) => {
        state.isProductLoading = true;
        state.productError = null;
      });

      try {
        const product = await api.products.getProduct(id);
        
        set((state) => {
          state.currentProduct = product as any;
        });
        
        return product as any;
      } catch (error: any) {
        set((state) => {
          state.productError = error.message || 'Failed to fetch product';
        });
        return null;
      } finally {
        set((state) => {
          state.isProductLoading = false;
        });
      }
    },

    // Fetch single product by slug
    fetchProductBySlug: async (slug) => {
      set((state) => {
        state.isProductLoading = true;
        state.productError = null;
      });

      try {
        const product = await api.products.getProduct(slug);
        
        set((state) => {
          state.currentProduct = product as any;
        });
        
        return product as any;
      } catch (error: any) {
        set((state) => {
          state.productError = error.message || 'Failed to fetch product';
        });
        return null;
      } finally {
        set((state) => {
          state.isProductLoading = false;
        });
      }
    },

    // Search products
    searchProducts: async (query, filters = {}) => {
      set((state) => {
        state.isSearching = true;
        state.searchQuery = query;
      });

      try {
        const searchParams = {
          q: query,
          ...filters,
        };

        const response = await api.products.searchProducts(query, filters);
        
        set((state) => {
          state.searchResults = response.products as any;
        });
      } catch (error: any) {
        console.error('Search failed:', error);
        set((state) => {
          state.searchResults = [];
        });
      } finally {
        set((state) => {
          state.isSearching = false;
        });
      }
    },

    // Clear search
    clearSearch: () => {
      set((state) => {
        state.searchQuery = '';
        state.searchResults = [];
      });
    },

    // Set filters
    setFilters: (filters) => {
      set((state) => {
        state.filters = { ...state.filters, ...filters };
      });
      
      // Auto-fetch with new filters
      get().fetchProducts({ filters: get().filters, page: 1 });
    },

    // Clear filters
    clearFilters: () => {
      set((state) => {
        state.filters = {};
      });
      
      get().fetchProducts({ page: 1 });
    },

    // Set sort
    setSort: (sort) => {
      set((state) => {
        state.sort = sort;
      });
      
      get().fetchProducts({ sort, page: 1 });
    },

    // Set pagination
    setPagination: (page, limit) => {
      const newLimit = limit || get().pagination.limit;
      
      set((state) => {
        state.pagination.page = page;
        if (limit) {
          state.pagination.limit = limit;
        }
      });
      
      get().fetchProducts({ page, limit: newLimit });
    },

    // Fetch categories
    fetchCategories: async () => {
      try {
        const categories = await api.products.getCategories();
        
        set((state) => {
          state.categories = categories as any;
        });
      } catch (error: any) {
        console.error('Failed to fetch categories:', error);
      }
    },

    // Create product (for sellers)
    createProduct: async (productData) => {
      try {
        const product = await api.products.createProduct(productData as any);

        set((state) => {
          state.products.unshift(product as any);
        });

        return product as any;
      } catch (error: any) {
        throw new Error(error.message || 'Failed to create product');
      }
    },

    // Update product
    updateProduct: async (id, productData) => {
      try {
        const product = await api.products.updateProduct(id, productData as any);

        set((state) => {
          const index = state.products.findIndex(p => p.id === id);
          if (index >= 0) {
            state.products[index] = product as any;
          }

          if (state.currentProduct?.id === id) {
            state.currentProduct = product as any;
          }
        });

        return product as any;
      } catch (error: any) {
        throw new Error(error.message || 'Failed to update product');
      }
    },

    // Delete product
    deleteProduct: async (id) => {
      try {
        await api.products.deleteProduct(id);
        
        set((state) => {
          state.products = state.products.filter(p => p.id !== id);
          
          if (state.currentProduct?.id === id) {
            state.currentProduct = null;
          }
        });
      } catch (error: any) {
        throw new Error(error.message || 'Failed to delete product');
      }
    },

    // Utility functions
    getProductById: (id) => {
      return get().products.find(p => p.id === id);
    },

    getProductsByCategory: (categoryId) => {
      return get().products.filter(p => p.category.id === categoryId);
    },

    getProductsBySeller: (sellerId) => {
      return get().products.filter(p => p.seller.id === sellerId);
    },

    getRelatedProducts: (productId, limit = 5) => {
      const product = get().getProductById(productId);
      if (!product) return [];
      
      return get().products
        .filter(p => 
          p.id !== productId && 
          (p.category.id === product.category.id || 
           p.seller.id === product.seller.id)
        )
        .slice(0, limit);
    },

    // Cache management
    invalidateCache: () => {
      set((state) => {
        state.products = [];
        state.featuredProducts = [];
        state.currentProduct = null;
      });
    },

    refreshProducts: async () => {
      await get().fetchProducts({
        filters: get().filters,
        sort: get().sort,
        page: get().pagination.page,
        limit: get().pagination.limit,
      });
    },

    // Reset store
    reset: () => {
      set((state) => {
        state.products = [];
        state.featuredProducts = [];
        state.currentProduct = null;
        state.categories = [];
        state.isLoading = false;
        state.isFeaturedLoading = false;
        state.isProductLoading = false;
        state.error = null;
        state.productError = null;
        state.filters = {};
        state.sort = initialSort;
        state.pagination = initialPagination;
        state.searchQuery = '';
        state.searchResults = [];
        state.isSearching = false;
      });
    },
  }))
);
