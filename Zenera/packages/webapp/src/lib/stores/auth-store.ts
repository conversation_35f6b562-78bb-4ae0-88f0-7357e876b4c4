/**
 * Zustand Auth Store
 * Adapted từ Medoo patterns cho Zenera E-commerce
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { api } from '../api';
import { ZeneraCookiesMethod, AuthenticationUtils } from '../utils/cookies-method';
import { DeviceUuidUtils } from '../utils/device-uuid-utils';
import { PermissionUtils } from '../utils/permission-utils';
import type { User, AuthResponse, LoginRequest, RegisterRequest } from '../api/auth';

// Auth Store State Interface
export interface AuthState {
  // User data
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  
  // Permissions & Roles
  userPermissions: string[];
  userRoles: string[];
  
  // Loading states
  isLoading: boolean;
  isInitializing: boolean;
  
  // Device info
  deviceUuid: string;
  
  // Actions
  login: (credentials: LoginRequest) => Promise<AuthResponse>;
  register: (userData: RegisterRequest) => Promise<AuthResponse>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  getProfile: () => Promise<User>;
  
  // Permission helpers
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  hasRole: (role: string) => boolean;
  
  // Initialization
  initialize: () => Promise<void>;
  
  // Internal methods
  setUser: (user: User | null) => void;
  setToken: (token: string | null) => void;
  setLoading: (loading: boolean) => void;
  clearAuth: () => void;
}

/**
 * Zenera Auth Store
 * Sử dụng Medoo patterns với Zustand
 */
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      userPermissions: [],
      userRoles: [],
      isLoading: false,
      isInitializing: true,
      deviceUuid: '',

      /**
       * Login user
       * Sử dụng Medoo's authentication logic
       */
      login: async (credentials: LoginRequest): Promise<AuthResponse> => {
        set({ isLoading: true });

        try {
          // Device info được track riêng, không gửi trong API request
          const deviceInfo = DeviceUuidUtils.getDeviceInfo();

          const response = await api.auth.login(credentials);
          
          // Handle login success với Medoo pattern
          AuthenticationUtils.onLoginSuccess(response, () => {
            const effectivePermissions = PermissionUtils.getEffectivePermissions(
              response.user.roles || [],
              (response.user as any).permissions || []
            );

            set({
              user: response.user,
              token: response.access_token,
              refreshToken: response.refresh_token || null,
              isAuthenticated: true,
              userPermissions: effectivePermissions,
              userRoles: response.user.roles || [],
              isLoading: false,
              deviceUuid: deviceInfo.uuid,
            });
          });

          return response;
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      /**
       * Register new user
       */
      register: async (userData: RegisterRequest): Promise<AuthResponse> => {
        set({ isLoading: true });

        try {
          // Device info được track riêng, không gửi trong API request
          const deviceInfo = DeviceUuidUtils.getDeviceInfo();

          const response = await api.auth.register(userData);
          
          // Handle registration success
          AuthenticationUtils.onLoginSuccess(response, () => {
            const effectivePermissions = PermissionUtils.getEffectivePermissions(
              response.user.roles || [],
              (response.user as any).permissions || []
            );

            set({
              user: response.user,
              token: response.access_token,
              refreshToken: response.refresh_token || null,
              isAuthenticated: true,
              userPermissions: effectivePermissions,
              userRoles: response.user.roles || [],
              isLoading: false,
              deviceUuid: deviceInfo.uuid,
            });
          });

          return response;
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      /**
       * Logout user
       */
      logout: async (): Promise<void> => {
        set({ isLoading: true });
        
        try {
          // Call logout API
          await api.auth.logout();
        } catch (error) {
          // Continue with logout even if API fails
          console.warn('Logout API failed:', error);
        } finally {
          // Clear auth state và cookies
          AuthenticationUtils.onLogout(() => {
            set({
              user: null,
              token: null,
              refreshToken: null,
              isAuthenticated: false,
              userPermissions: [],
              userRoles: [],
              isLoading: false,
            });
          });
        }
      },

      /**
       * Refresh authentication
       */
      refreshAuth: async (): Promise<void> => {
        const { refreshToken } = get();
        
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        try {
          const response = await api.auth.refreshToken();
          
          // Update auth state
          AuthenticationUtils.onLoginSuccess(response, () => {
            const effectivePermissions = PermissionUtils.getEffectivePermissions(
              response.user.roles || [],
              response.user.permissions || []
            );

            set({
              user: response.user,
              token: response.access_token,
              refreshToken: response.refresh_token || refreshToken,
              userPermissions: effectivePermissions,
              userRoles: response.user.roles || [],
            });
          });
        } catch (error) {
          // If refresh fails, logout user
          get().logout();
          throw error;
        }
      },

      /**
       * Get user profile
       */
      getProfile: async (): Promise<User> => {
        const user = await api.auth.getProfile();
        
        const effectivePermissions = PermissionUtils.getEffectivePermissions(
          user.roles || [],
          user.permissions || []
        );

        set({
          user,
          userPermissions: effectivePermissions,
          userRoles: user.roles || [],
        });

        return user;
      },

      /**
       * Permission helpers
       */
      hasPermission: (permission: string): boolean => {
        const { userPermissions } = get();
        return PermissionUtils.hasPermission(permission, userPermissions);
      },

      hasAnyPermission: (permissions: string[]): boolean => {
        const { userPermissions } = get();
        return PermissionUtils.hasAnyPermission(permissions, userPermissions);
      },

      hasAllPermissions: (permissions: string[]): boolean => {
        const { userPermissions } = get();
        return PermissionUtils.hasAllPermissions(permissions, userPermissions);
      },

      hasRole: (role: string): boolean => {
        const { userRoles } = get();
        return userRoles.includes(role);
      },

      /**
       * Initialize auth state từ cookies
       */
      initialize: async (): Promise<void> => {
        set({ isInitializing: true });
        
        try {
          // Initialize device UUID
          const deviceUuid = DeviceUuidUtils.initialize();
          
          // Get auth state từ cookies
          const authState = AuthenticationUtils.getAuthState();
          
          if (authState.isAuthenticated && !authState.isExpired) {
            // Restore auth state
            const effectivePermissions = PermissionUtils.getEffectivePermissions(
              authState.user?.roles || [],
              authState.permissions
            );

            set({
              user: authState.user,
              token: authState.token,
              refreshToken: authState.refreshToken,
              isAuthenticated: true,
              userPermissions: effectivePermissions,
              userRoles: authState.user?.roles || [],
              deviceUuid,
            });

            // Try to refresh profile
            try {
              await get().getProfile();
            } catch (error) {
              console.warn('Failed to refresh profile:', error);
            }
          } else {
            // Clear expired auth
            AuthenticationUtils.resetCookies();
            set({
              user: null,
              token: null,
              refreshToken: null,
              isAuthenticated: false,
              userPermissions: [],
              userRoles: [],
              deviceUuid,
            });
          }
        } catch (error) {
          console.error('Auth initialization failed:', error);
          AuthenticationUtils.resetCookies();
          set({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            userPermissions: [],
            userRoles: [],
            deviceUuid: DeviceUuidUtils.initialize(),
          });
        } finally {
          set({ isInitializing: false });
        }
      },

      /**
       * Internal methods
       */
      setUser: (user: User | null): void => {
        set({ user });
      },

      setToken: (token: string | null): void => {
        set({ token });
      },

      setLoading: (loading: boolean): void => {
        set({ isLoading: loading });
      },

      clearAuth: (): void => {
        AuthenticationUtils.resetCookies();
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          userPermissions: [],
          userRoles: [],
          isLoading: false,
        });
      },
    }),
    {
      name: 'zenera-auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        // Only persist essential data
        deviceUuid: state.deviceUuid,
        // Don't persist sensitive data - use cookies instead
      }),
    }
  )
);
