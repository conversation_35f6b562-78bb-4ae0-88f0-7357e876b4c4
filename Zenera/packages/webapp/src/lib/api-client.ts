/**
 * Zenera API Client
 * Centralized API communication layer với error handling và type safety
 */

// API Configuration with dynamic port detection
const getApiBaseUrl = (): string => {
  // Check environment variable first
  if (process.env.NEXT_PUBLIC_API_URL) {
    return process.env.NEXT_PUBLIC_API_URL;
  }

  // Default ports to try in order
  const defaultPorts = [3001, 3002, 3003, 3004, 3005, 3006];

  // For server-side rendering, use the first default port
  if (typeof window === 'undefined') {
    return `http://localhost:${defaultPorts[0]}`;
  }

  // For client-side, we'll detect the working port later
  return `http://localhost:${defaultPorts[0]}`;
};

const API_BASE_URL = getApiBaseUrl();
const API_VERSION = 'v1';

// Port detection utility
export const detectApiPort = async (): Promise<string> => {
  const ports = [3001, 3002, 3003, 3004, 3005, 3006];

  for (const port of ports) {
    try {
      const response = await fetch(`http://localhost:${port}/v1/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(2000), // 2 second timeout
      });

      if (response.ok) {
        console.log(`✅ API Server detected on port ${port}`);
        return `http://localhost:${port}`;
      }
    } catch (error) {
      // Port not available, try next
      continue;
    }
  }

  console.warn('⚠️ No API server detected on any port, using default');
  return `http://localhost:3001`;
};

export const API_ENDPOINTS = {
  // Health check
  HEALTH: '/health',
  
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
  },
  
  // Users
  USERS: {
    BASE: '/users',
    BY_ID: (id: string) => `/users/${id}`,
  },
  
  // Products
  PRODUCTS: {
    BASE: '/products',
    BY_ID: (id: string) => `/products/${id}`,
    SEARCH: '/products/search',
    CATEGORIES: '/products/categories',
  },
  
  // Orders
  ORDERS: {
    BASE: '/orders',
    BY_ID: (id: string) => `/orders/${id}`,
    BY_USER: (userId: string) => `/orders/user/${userId}`,
  },
  
  // Cart
  CART: {
    BASE: '/cart',
    ADD_ITEM: '/cart/items',
    UPDATE_ITEM: (itemId: string) => `/cart/items/${itemId}`,
    REMOVE_ITEM: (itemId: string) => `/cart/items/${itemId}`,
  },
} as const;

// Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
  timestamp: string;
}

export interface ApiError {
  message: string;
  error: string;
  statusCode: number;
  timestamp: string;
}

export interface RequestConfig extends RequestInit {
  timeout?: number;
  retries?: number;
  skipAuth?: boolean;
  skipErrorHandling?: boolean;
}

// Request/Response Interceptor types
export type RequestInterceptor = (config: RequestConfig) => RequestConfig | Promise<RequestConfig>;
export type ResponseInterceptor = (response: Response) => Response | Promise<Response>;
export type ErrorInterceptor = (error: Error) => Error | Promise<Error>;

// Custom Error Classes
export class ApiClientError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public response?: Response
  ) {
    super(message);
    this.name = 'ApiClientError';
  }
}

export class NetworkError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message);
    this.name = 'NetworkError';
  }
}

// API Client Class
class ApiClient {
  private baseUrl: string;
  private defaultHeaders: HeadersInit;
  private requestInterceptors: RequestInterceptor[] = [];
  private responseInterceptors: ResponseInterceptor[] = [];
  private errorInterceptors: ErrorInterceptor[] = [];

  constructor() {
    this.baseUrl = `${API_BASE_URL}/${API_VERSION}`;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };

    // Setup default interceptors
    this.setupDefaultInterceptors();

    // Auto-detect API port on client-side
    if (typeof window !== 'undefined') {
      this.detectAndUpdateApiPort();
    }
  }

  /**
   * Detect and update API port dynamically
   */
  private async detectAndUpdateApiPort(): Promise<void> {
    try {
      const detectedBaseUrl = await detectApiPort();
      if (detectedBaseUrl !== API_BASE_URL) {
        this.baseUrl = `${detectedBaseUrl}/${API_VERSION}`;
        console.log(`🔄 API base URL updated to: ${this.baseUrl}`);
      }
    } catch (error) {
      console.warn('⚠️ Failed to detect API port, using default:', error);
    }
  }

  /**
   * Manually update base URL (useful for testing)
   */
  updateBaseUrl(newBaseUrl: string): void {
    this.baseUrl = `${newBaseUrl}/${API_VERSION}`;
    console.log(`🔄 API base URL manually updated to: ${this.baseUrl}`);
  }

  /**
   * Setup default interceptors
   */
  private setupDefaultInterceptors(): void {
    // Request interceptor for logging
    this.addRequestInterceptor((config) => {
      if (process.env.NODE_ENV === 'development') {
        console.log(`🚀 API Request: ${config.method || 'GET'}`, config);
      }
      return config;
    });

    // Response interceptor for logging
    this.addResponseInterceptor((response) => {
      if (process.env.NODE_ENV === 'development') {
        console.log(`✅ API Response: ${response.status}`, response);
      }
      return response;
    });

    // Error interceptor for auth handling
    this.addErrorInterceptor((error) => {
      if (error instanceof ApiClientError && error.statusCode === 401) {
        // Handle unauthorized - clear auth and redirect
        this.clearAuthToken();
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
      }
      return error;
    });
  }

  /**
   * Add request interceptor
   */
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor);
  }

  /**
   * Add response interceptor
   */
  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor);
  }

  /**
   * Add error interceptor
   */
  addErrorInterceptor(interceptor: ErrorInterceptor): void {
    this.errorInterceptors.push(interceptor);
  }

  /**
   * Apply request interceptors
   */
  private async applyRequestInterceptors(config: RequestConfig): Promise<RequestConfig> {
    let processedConfig = config;

    for (const interceptor of this.requestInterceptors) {
      processedConfig = await interceptor(processedConfig);
    }

    return processedConfig;
  }

  /**
   * Apply response interceptors
   */
  private async applyResponseInterceptors(response: Response): Promise<Response> {
    let processedResponse = response;

    for (const interceptor of this.responseInterceptors) {
      processedResponse = await interceptor(processedResponse);
    }

    return processedResponse;
  }

  /**
   * Apply error interceptors
   */
  private async applyErrorInterceptors(error: Error): Promise<Error> {
    let processedError = error;

    for (const interceptor of this.errorInterceptors) {
      processedError = await interceptor(processedError);
    }

    return processedError;
  }

  /**
   * Get authorization header từ localStorage hoặc cookies
   */
  private getAuthHeader(skipAuth?: boolean): HeadersInit {
    if (skipAuth || typeof window === 'undefined') return {};

    const token = localStorage.getItem('auth_token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  /**
   * Build full URL
   */
  private buildUrl(endpoint: string): string {
    return `${this.baseUrl}${endpoint}`;
  }

  /**
   * Handle API response với interceptors
   */
  private async handleResponse<T>(response: Response, skipErrorHandling?: boolean): Promise<T> {
    try {
      // Apply response interceptors
      const processedResponse = await this.applyResponseInterceptors(response);

      const contentType = processedResponse.headers.get('content-type');
      const isJson = contentType?.includes('application/json');

      let data: any;
      try {
        data = isJson ? await processedResponse.json() : await processedResponse.text();
      } catch (error) {
        const parseError = new ApiClientError(
          'Failed to parse response',
          processedResponse.status,
          processedResponse
        );
        throw await this.applyErrorInterceptors(parseError);
      }

      if (!processedResponse.ok && !skipErrorHandling) {
        const errorMessage = data?.message || data?.error || 'API request failed';
        const apiError = new ApiClientError(errorMessage, processedResponse.status, processedResponse);
        throw await this.applyErrorInterceptors(apiError);
      }

      return data;
    } catch (error) {
      if (error instanceof ApiClientError) {
        throw error;
      }
      throw await this.applyErrorInterceptors(error as Error);
    }
  }

  /**
   * Make HTTP request với retry logic và interceptors
   */
  private async makeRequest<T>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<T> {
    const { timeout = 10000, retries = 3, skipAuth, skipErrorHandling, ...fetchConfig } = config;

    // Apply request interceptors
    const processedConfig = await this.applyRequestInterceptors({
      ...config,
      method: fetchConfig.method || 'GET',
    });

    const url = this.buildUrl(endpoint);
    const headers = {
      ...this.defaultHeaders,
      ...this.getAuthHeader(skipAuth),
      ...fetchConfig.headers,
      ...processedConfig.headers,
    };

    const requestConfig: RequestInit = {
      ...fetchConfig,
      headers,
    };

    let lastError: Error = new Error('Unknown error');

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
          ...requestConfig,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);
        return await this.handleResponse<T>(response, skipErrorHandling);
      } catch (error) {
        lastError = error as Error;

        // Don't retry on client errors (4xx)
        if (error instanceof ApiClientError && error.statusCode < 500) {
          throw error;
        }

        // Don't retry on last attempt
        if (attempt === retries) {
          break;
        }

        // Wait before retry (exponential backoff)
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    const networkError = new NetworkError(
      `Request failed after ${retries + 1} attempts: ${lastError.message}`,
      lastError
    );
    throw await this.applyErrorInterceptors(networkError);
  }

  // HTTP Methods
  async get<T>(endpoint: string, config?: RequestConfig): Promise<T> {
    return this.makeRequest<T>(endpoint, { ...config, method: 'GET' });
  }

  async post<T>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    return this.makeRequest<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    return this.makeRequest<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    return this.makeRequest<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string, config?: RequestConfig): Promise<T> {
    return this.makeRequest<T>(endpoint, { ...config, method: 'DELETE' });
  }

  // Utility methods
  setAuthToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  clearAuthToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  getAuthToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('auth_token');
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Export for testing
export { ApiClient };
