/**
 * Schema-driven Form System
 * Inspired by Medoo's HForm patterns for dynamic form generation
 */

import { z } from "zod";
import { Control, FieldPath, FieldValues } from "react-hook-form";

/**
 * Form Field Types (inspired by Medoo)
 */
export type FormFieldType = 
  | 'input'
  | 'textarea'
  | 'select'
  | 'checkbox'
  | 'radio'
  | 'file'
  | 'date'
  | 'number'
  | 'email'
  | 'password'
  | 'phone'
  | 'url'
  | 'search'
  | 'color'
  | 'range'
  | 'time'
  | 'datetime-local'
  | 'month'
  | 'week'
  | 'hidden'
  | 'custom';

/**
 * Form Field Schema Item (inspired by Medoo's createSchemaItem)
 */
export interface FormSchemaItem {
  name: string;
  type: FormFieldType;
  label?: string;
  placeholder?: string;
  hint?: string;
  required?: boolean;
  disabled?: boolean;
  hidden?: boolean;
  defaultValue?: any;
  
  // Validation
  validation?: z.ZodType<any>;
  rules?: Array<{
    validator: (value: any) => boolean | string;
    message: string;
  }>;
  
  // Component props
  componentProps?: Record<string, any>;
  
  // Layout
  colSpan?: number;
  rowSpan?: number;
  className?: string;
  
  // Conditional rendering
  when?: {
    field: string;
    condition: (value: any) => boolean;
  };
  
  // Options for select/radio
  options?: Array<{
    value: string | number;
    label: string;
    disabled?: boolean;
  }>;
  
  // Async options loading
  endpoint?: string;
  optionsLoader?: () => Promise<Array<{ value: string | number; label: string }>>;
  
  // File upload specific
  accept?: string;
  multiple?: boolean;
  maxSize?: number;
  maxFiles?: number;
  
  // Number specific
  min?: number;
  max?: number;
  step?: number;
  
  // Text specific
  maxLength?: number;
  minLength?: number;
  
  // Custom component
  Component?: React.ComponentType<any>;
  
  // Event handlers
  onChange?: (value: any, allValues: any) => void;
  onBlur?: (value: any) => void;
  onFocus?: (value: any) => void;
  
  // Dependencies
  dependencies?: string[];
  
  // Transform
  transform?: {
    input?: (value: any) => any;
    output?: (value: any) => any;
  };
}

/**
 * Form Schema (inspired by Medoo's schema functions)
 */
export type FormSchema = FormSchemaItem[] | (() => FormSchemaItem[]);

/**
 * Form Section for grouping fields
 */
export interface FormSection {
  title?: string;
  description?: string;
  fields: FormSchemaItem[];
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  className?: string;
  when?: {
    field: string;
    condition: (value: any) => boolean;
  };
}

/**
 * Advanced Form Configuration (inspired by Medoo's HFormProps)
 */
export interface AdvancedFormConfig {
  schema: FormSchema;
  sections?: FormSection[];
  
  // API Integration
  endpoint?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  
  // Form behavior
  resetOnSuccess?: boolean;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  
  // UI Configuration
  layout?: 'vertical' | 'horizontal' | 'inline';
  size?: 'sm' | 'md' | 'lg';
  
  // Submission
  onSubmit?: (data: any) => Promise<any>;
  onSuccess?: (response: any) => void;
  onError?: (error: any) => void;
  onDataReadyToSubmit?: (data: any) => any;
  
  // Loading states
  loading?: boolean;
  submitting?: boolean;
  
  // Buttons
  hideSubmitButton?: boolean;
  hideResetButton?: boolean;
  submitButtonText?: string;
  resetButtonText?: string;
  
  // Advanced features
  withConfirmation?: boolean;
  confirmationMessage?: string;
  withAutoSave?: boolean;
  autoSaveInterval?: number;
  
  // Multi-language support
  i18n?: {
    t: (key: string, options?: any) => string;
    locale: string;
  };
  
  // Custom styling
  className?: string;
  fieldClassName?: string;
  
  // Debug mode
  debug?: boolean;
}

/**
 * Create Schema Item helper (inspired by Medoo's createSchemaItem)
 */
export function createSchemaItem(config: Partial<FormSchemaItem> & { name: string; type: FormFieldType }): FormSchemaItem {
  return {
    required: false,
    disabled: false,
    hidden: false,
    colSpan: 1,
    rowSpan: 1,
    ...config,
  };
}

/**
 * Create Input Schema Item
 */
export function createInputSchemaItem(config: Partial<FormSchemaItem> & { name: string }): FormSchemaItem {
  return createSchemaItem({
    type: 'input',
    ...config,
  });
}

/**
 * Create Select Schema Item
 */
export function createSelectSchemaItem(
  config: Partial<FormSchemaItem> & { 
    name: string; 
    options: Array<{ value: string | number; label: string }> 
  }
): FormSchemaItem {
  return createSchemaItem({
    type: 'select',
    ...config,
  });
}

/**
 * Create Checkbox Schema Item
 */
export function createCheckboxSchemaItem(config: Partial<FormSchemaItem> & { name: string }): FormSchemaItem {
  return createSchemaItem({
    type: 'checkbox',
    ...config,
  });
}

/**
 * Create Textarea Schema Item
 */
export function createTextareaSchemaItem(config: Partial<FormSchemaItem> & { name: string }): FormSchemaItem {
  return createSchemaItem({
    type: 'textarea',
    ...config,
  });
}

/**
 * Create File Upload Schema Item
 */
export function createFileSchemaItem(config: Partial<FormSchemaItem> & { name: string }): FormSchemaItem {
  return createSchemaItem({
    type: 'file',
    ...config,
  });
}

/**
 * Create Phone/Email Schema Item (inspired by Medoo's createPhoneOrEmailSchemaItem)
 */
export function createPhoneOrEmailSchemaItem(config: {
  name: string;
  usingPhoneNumber?: boolean;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  hint?: string;
}): FormSchemaItem {
  const { usingPhoneNumber = false, ...rest } = config;
  
  return createSchemaItem({
    type: usingPhoneNumber ? 'phone' : 'email',
    label: usingPhoneNumber ? 'Số điện thoại' : 'Email',
    validation: usingPhoneNumber 
      ? z.string().regex(/^[0-9+\-\s()]+$/, 'Số điện thoại không hợp lệ')
      : z.string().email('Email không hợp lệ'),
    ...rest,
  });
}

/**
 * Schema Utilities
 */
export const SchemaUtils = {
  /**
   * Resolve schema (handle function schemas)
   */
  resolveSchema: (schema: FormSchema, context?: any): FormSchemaItem[] => {
    if (typeof schema === 'function') {
      return schema();
    }
    return schema;
  },

  /**
   * Filter visible fields based on conditions
   */
  filterVisibleFields: (fields: FormSchemaItem[], formValues: any): FormSchemaItem[] => {
    return fields.filter(field => {
      if (field.hidden) return false;
      
      if (field.when) {
        const { field: dependentField, condition } = field.when;
        const dependentValue = formValues[dependentField];
        return condition(dependentValue);
      }
      
      return true;
    });
  },

  /**
   * Get field dependencies
   */
  getFieldDependencies: (fields: FormSchemaItem[]): Record<string, string[]> => {
    const dependencies: Record<string, string[]> = {};
    
    fields.forEach(field => {
      if (field.dependencies) {
        dependencies[field.name] = field.dependencies;
      }
      
      if (field.when) {
        if (!dependencies[field.name]) {
          dependencies[field.name] = [];
        }
        dependencies[field.name].push(field.when.field);
      }
    });
    
    return dependencies;
  },

  /**
   * Generate validation schema from form schema
   */
  generateValidationSchema: (fields: FormSchemaItem[]): z.ZodObject<any> => {
    const shape: Record<string, z.ZodType<any>> = {};

    // Handle case where fields might be undefined or not an array
    if (!fields || !Array.isArray(fields)) {
      return z.object({});
    }

    fields.forEach(field => {
      if (field.validation) {
        shape[field.name] = field.validation;
      } else {
        // Generate basic validation based on field type
        let fieldSchema: z.ZodType<any> = z.any();
        
        switch (field.type) {
          case 'email':
            fieldSchema = z.string().email('Email không hợp lệ');
            break;
          case 'phone':
            fieldSchema = z.string().regex(/^[0-9+\-\s()]+$/, 'Số điện thoại không hợp lệ');
            break;
          case 'url':
            fieldSchema = z.string().url('URL không hợp lệ');
            break;
          case 'number':
            let numberSchema = z.number();
            if (field.min !== undefined) numberSchema = numberSchema.min(field.min);
            if (field.max !== undefined) numberSchema = numberSchema.max(field.max);
            fieldSchema = numberSchema;
            break;
          case 'input':
          case 'textarea':
          case 'password':
            let stringSchema = z.string();
            if (field.minLength) stringSchema = stringSchema.min(field.minLength);
            if (field.maxLength) stringSchema = stringSchema.max(field.maxLength);
            fieldSchema = stringSchema;
            break;
          case 'checkbox':
            fieldSchema = z.boolean();
            break;
          case 'file':
            fieldSchema = field.multiple ? z.array(z.instanceof(File)) : z.instanceof(File);
            break;
          default:
            fieldSchema = z.string();
        }
        
        if (field.required) {
          if (fieldSchema instanceof z.ZodString) {
            fieldSchema = fieldSchema.min(1, `${field.label || field.name} là bắt buộc`);
          }
        } else {
          fieldSchema = fieldSchema.optional();
        }
        
        shape[field.name] = fieldSchema;
      }
    });
    
    return z.object(shape);
  },
};
