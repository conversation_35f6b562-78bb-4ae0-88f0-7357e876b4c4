"use client";

import { usePara<PERSON>, useRouter, usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { 
  Languages, 
  DEFAULT_LOCALE, 
  LOCALE_COOKIE_NAME, 
  LOCALE_STORAGE_KEY,
  isValidLanguage 
} from "@/lib/constants/languages";

/**
 * Hook for locale management
 * Inspired by Medoo's useLocale
 */
export const useLocale = () => {
  const params = useParams();
  const [locale, setLocaleState] = useState<string>(DEFAULT_LOCALE);

  useEffect(() => {
    // Priority like Medoo: path > cookie > localStorage > default
    let detectedLocale = DEFAULT_LOCALE;

    // Check URL params first (path detection)
    if (params?.locale && isValidLanguage(params.locale as string)) {
      detectedLocale = params.locale as Languages;
    } else {
      // Check cookie (like Medoo's NEXT_LOCALE)
      const cookieLocale = Cookies.get(LOCALE_COOKIE_NAME);
      if (cookieLocale && isValidLanguage(cookieLocale)) {
        detectedLocale = cookieLocale;
      } else {
        // Check localStorage as fallback
        const storageLocale = localStorage.getItem(LOCALE_STORAGE_KEY);
        if (storageLocale && isValidLanguage(storageLocale)) {
          detectedLocale = storageLocale;
        }
      }
    }

    setLocaleState(detectedLocale);

    // Sync cookie if different (like Medoo pattern)
    if (Cookies.get(LOCALE_COOKIE_NAME) !== detectedLocale) {
      Cookies.set(LOCALE_COOKIE_NAME, detectedLocale, {
        expires: 365,
        path: '/',
        sameSite: 'lax'
      });
    }
  }, [params?.locale]);

  return locale;
};

/**
 * Hook for locale switching
 * Inspired by Medoo's locale switching utilities
 */
export const useLocaleSwitch = () => {
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = useLocale();

  const switchLocale = (newLocale: string) => {
    if (!isValidLanguage(newLocale)) {
      console.warn(`Invalid locale: ${newLocale}`);
      return;
    }

    // Save to cookie and localStorage
    Cookies.set(LOCALE_COOKIE_NAME, newLocale, {
      expires: 365, // 1 year
      path: '/',
      sameSite: 'lax'
    });
    localStorage.setItem(LOCALE_STORAGE_KEY, newLocale);

    // Update URL if needed
    if (pathname) {
      // Remove current locale from path if present
      const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}/, '') || '/';
      const newPath = `/${newLocale}${pathWithoutLocale === '/' ? '' : pathWithoutLocale}`;

      // Use window.location.href for immediate redirect
      window.location.href = newPath;
    }
  };

  return {
    currentLocale,
    switchLocale,
    availableLocales: Object.values(Languages),
  };
};

/**
 * Hook for locale utilities
 */
export const useLocaleUtils = () => {
  const locale = useLocale();

  const getLocaleConfig = () => {
    return {
      code: locale,
      name: locale === Languages.EN ? 'English' : 'Tiếng Việt',
      nativeName: locale === Languages.EN ? 'English' : 'Tiếng Việt',
      flag: locale === Languages.EN ? '🇺🇸' : '🇻🇳',
      rtl: false,
    };
  };

  const isEnglish = locale === Languages.EN;
  const isVietnamese = locale === Languages.VI;

  const formatCurrency = (amount: number) => {
    if (isVietnamese) {
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
      }).format(amount);
    } else {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(amount);
    }
  };

  const formatNumber = (number: number) => {
    return new Intl.NumberFormat(locale === Languages.VI ? 'vi-VN' : 'en-US').format(number);
  };

  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale === Languages.VI ? 'vi-VN' : 'en-US').format(dateObj);
  };

  const formatDateTime = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale === Languages.VI ? 'vi-VN' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(dateObj);
  };

  const formatRelativeTime = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const rtf = new Intl.RelativeTimeFormat(locale === Languages.VI ? 'vi-VN' : 'en-US', {
      numeric: 'auto'
    });

    const diffInSeconds = (dateObj.getTime() - Date.now()) / 1000;
    const diffInMinutes = diffInSeconds / 60;
    const diffInHours = diffInMinutes / 60;
    const diffInDays = diffInHours / 24;

    if (Math.abs(diffInDays) >= 1) {
      return rtf.format(Math.round(diffInDays), 'day');
    } else if (Math.abs(diffInHours) >= 1) {
      return rtf.format(Math.round(diffInHours), 'hour');
    } else {
      return rtf.format(Math.round(diffInMinutes), 'minute');
    }
  };

  return {
    locale,
    getLocaleConfig,
    isEnglish,
    isVietnamese,
    formatCurrency,
    formatNumber,
    formatDate,
    formatDateTime,
    formatRelativeTime,
  };
};

/**
 * Hook for browser locale detection
 */
export const useBrowserLocale = () => {
  const [browserLocale, setBrowserLocale] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const detected = navigator.language.split('-')[0];
      setBrowserLocale(isValidLanguage(detected) ? detected : DEFAULT_LOCALE);
    }
  }, []);

  return browserLocale;
};

/**
 * Hook for locale persistence
 */
export const useLocalePersistence = () => {
  const saveLocale = (locale: string) => {
    if (!isValidLanguage(locale)) return;

    Cookies.set(LOCALE_COOKIE_NAME, locale, { 
      expires: 365,
      path: '/',
      sameSite: 'lax'
    });
    localStorage.setItem(LOCALE_STORAGE_KEY, locale);
  };

  const getPersistedLocale = (): string => {
    // Check cookie first
    const cookieLocale = Cookies.get(LOCALE_COOKIE_NAME);
    if (cookieLocale && isValidLanguage(cookieLocale)) {
      return cookieLocale;
    }

    // Check localStorage
    const storageLocale = localStorage.getItem(LOCALE_STORAGE_KEY);
    if (storageLocale && isValidLanguage(storageLocale)) {
      return storageLocale;
    }

    return DEFAULT_LOCALE;
  };

  const clearPersistedLocale = () => {
    Cookies.remove(LOCALE_COOKIE_NAME);
    localStorage.removeItem(LOCALE_STORAGE_KEY);
  };

  return {
    saveLocale,
    getPersistedLocale,
    clearPersistedLocale,
  };
};
