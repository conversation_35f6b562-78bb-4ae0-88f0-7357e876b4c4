"use client";

import { useTranslation, UseTranslationOptions } from "react-i18next";
import { useEffect, useState, useCallback } from "react";
import { useLocale } from "./use-locale";
import { Languages } from "@/lib/constants/languages";

/**
 * Enhanced useTranslation hook
 * Inspired by Medoo's useHTranslation
 */
export const useZeneraTranslation = (
  namespace: string = "translation",
  options?: UseTranslationOptions<any>,
) => {
  const { t, i18n, ...trans } = useTranslation(namespace, options);
  const locale = useLocale();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    if (i18n.isInitialized) {
      setIsReady(true);
    }
  }, [i18n.isInitialized]);

  useEffect(() => {
    if (locale && i18n.language !== locale) {
      i18n.changeLanguage(locale);
    }
  }, [locale, i18n]);

  /**
   * Enhanced translate function with fallback support (like Medoo's useHTranslation)
   */
  const translate = useCallback((
    key: any,
    defaultValue: any = {},
    options?: any | string
  ) => {
    if (!isReady) {
      return key; // Return key while loading
    }

    // Handle default value like Medoo: object with locale keys or string
    const defaultKey =
      typeof defaultValue === "string"
        ? defaultValue
        : defaultValue[locale as string] || key;

    return t(key, defaultKey, options) || key;
  }, [t, isReady, locale]);

  /**
   * Translate with interpolation
   */
  const translateWithParams = (
    key: string,
    params: Record<string, any>,
    defaultValue?: string
  ) => {
    return translate(key, defaultValue, params);
  };

  /**
   * Translate array of keys
   */
  const translateMultiple = (keys: string[], defaultValues?: string[]) => {
    return keys.map((key, index) => 
      translate(key, defaultValues?.[index])
    );
  };

  /**
   * Check if translation exists
   */
  const exists = (key: string, namespace?: string) => {
    return i18n.exists(key, { ns: namespace || namespace });
  };

  /**
   * Get current language
   */
  const currentLanguage = i18n.language || locale || Languages.EN;

  /**
   * Check if current language is RTL
   */
  const isRTL = i18n?.dir ? i18n.dir() === 'rtl' : false;

  return {
    t: translate,
    translateWithParams,
    translateMultiple,
    exists,
    currentLanguage,
    isRTL,
    isReady,
    i18n,
    ...trans,
  };
};

/**
 * Shorthand hook for common translations
 */
export const useCommonTranslation = () => {
  return useZeneraTranslation("common");
};

/**
 * Hook for form translations
 */
export const useFormTranslation = () => {
  return useZeneraTranslation("form");
};

/**
 * Hook for validation translations
 */
export const useValidationTranslation = () => {
  return useZeneraTranslation("validation");
};

/**
 * Hook for multiple namespaces
 */
export const useMultipleTranslations = (namespaces: string[]) => {
  const translations = namespaces.reduce((acc, ns) => {
    acc[ns] = useZeneraTranslation(ns);
    return acc;
  }, {} as Record<string, ReturnType<typeof useZeneraTranslation>>);

  const isReady = Object.values(translations).every(t => t.isReady);

  return {
    translations,
    isReady,
  };
};

/**
 * Hook for getting translation function without component re-renders
 */
export const useTranslationFunction = (namespace: string = "translation") => {
  const { i18n } = useTranslation();
  
  return (key: string, defaultValue?: string, options?: any) => {
    return i18n.t(key, defaultValue || key, { ns: namespace, ...options });
  };
};

/**
 * Hook for language-specific content
 */
export const useContentByLocale = () => {
  const locale = useLocale();

  return (multiLanguageContent: any) => {
    if (!multiLanguageContent) return "";
    
    if (typeof multiLanguageContent === "string") {
      return multiLanguageContent;
    }
    
    if (typeof multiLanguageContent === "object") {
      return multiLanguageContent[locale] || 
             multiLanguageContent[Languages.EN] || 
             multiLanguageContent[Languages.VI] || 
             "";
    }
    
    return "";
  };
};

/**
 * Hook for data content by locale
 */
export const useDataContentByLocale = () => {
  const getContentByLocale = useContentByLocale();

  return (data: any) => {
    if (!data) return "";
    return typeof data === "object" ? getContentByLocale(data) : data;
  };
};

/**
 * Re-export useLocaleUtils from use-locale for convenience
 */
export { useLocaleUtils } from "./use-locale";

/**
 * Default export for convenience
 */
export default useZeneraTranslation;
