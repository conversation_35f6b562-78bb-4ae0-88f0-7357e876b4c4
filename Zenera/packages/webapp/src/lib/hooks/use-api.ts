/**
 * TanStack Query Hooks for Zenera API
 * Centralized API hooks với error handling và loading states
 */

import { 
  useQuery, 
  useMutation, 
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions,
  QueryKey,
} from '@tanstack/react-query';
import { apiClient, ApiResponse, ApiError } from '../api-client';

// Query Keys Factory
export const queryKeys = {
  // Health
  health: ['health'] as const,
  
  // Auth
  auth: {
    all: ['auth'] as const,
    profile: () => [...queryKeys.auth.all, 'profile'] as const,
  },
  
  // Users
  users: {
    all: ['users'] as const,
    list: (params?: any) => [...queryKeys.users.all, 'list', params] as const,
    detail: (id: string) => [...queryKeys.users.all, 'detail', id] as const,
  },
  
  // Products
  products: {
    all: ['products'] as const,
    list: (params?: any) => [...queryKeys.products.all, 'list', params] as const,
    detail: (id: string) => [...queryKeys.products.all, 'detail', id] as const,
    categories: () => [...queryKeys.products.all, 'categories'] as const,
    search: (query: string) => [...queryKeys.products.all, 'search', query] as const,
  },
  
  // Orders
  orders: {
    all: ['orders'] as const,
    list: (params?: any) => [...queryKeys.orders.all, 'list', params] as const,
    detail: (id: string) => [...queryKeys.orders.all, 'detail', id] as const,
    byUser: (userId: string) => [...queryKeys.orders.all, 'byUser', userId] as const,
  },
  
  // Cart
  cart: {
    all: ['cart'] as const,
    items: () => [...queryKeys.cart.all, 'items'] as const,
  },
} as const;

// Generic API Hook Types
export interface UseApiQueryOptions<TData = unknown, TError = ApiError> 
  extends Omit<UseQueryOptions<TData, TError>, 'queryKey' | 'queryFn'> {}

export interface UseApiMutationOptions<TData = unknown, TError = ApiError, TVariables = unknown>
  extends Omit<UseMutationOptions<TData, TError, TVariables>, 'mutationFn'> {}

// Health Check Hook
export const useHealthCheck = (options?: UseApiQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.health,
    queryFn: () => apiClient.get('/health'),
    staleTime: 30 * 1000, // 30 seconds
    ...options,
  });
};

// Generic Query Hook
export const useApiQuery = <TData = unknown>(
  queryKey: QueryKey,
  endpoint: string,
  options?: UseApiQueryOptions<TData>
) => {
  return useQuery({
    queryKey,
    queryFn: () => apiClient.get<TData>(endpoint),
    ...options,
  });
};

// Generic Mutation Hook
export const useApiMutation = <TData = unknown, TVariables = unknown>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: UseApiMutationOptions<TData, ApiError, TVariables>
) => {
  return useMutation({
    mutationFn,
    ...options,
  });
};

// Optimistic Update Helper
export const useOptimisticUpdate = () => {
  const queryClient = useQueryClient();

  const updateQueryData = <TData>(
    queryKey: QueryKey,
    updater: (oldData: TData | undefined) => TData
  ) => {
    queryClient.setQueryData(queryKey, updater);
  };

  const invalidateQueries = (queryKey: QueryKey) => {
    queryClient.invalidateQueries({ queryKey });
  };

  const refetchQueries = (queryKey: QueryKey) => {
    queryClient.refetchQueries({ queryKey });
  };

  return {
    updateQueryData,
    invalidateQueries,
    refetchQueries,
    queryClient,
  };
};

// Loading State Hook
export const useLoadingState = () => {
  const queryClient = useQueryClient();
  
  const getLoadingQueries = () => {
    return queryClient.getQueryCache().getAll().filter(query => query.state.fetchStatus === 'fetching');
  };

  const isAnyLoading = () => {
    return getLoadingQueries().length > 0;
  };

  const getLoadingByKey = (queryKey: QueryKey) => {
    const query = queryClient.getQueryCache().find({ queryKey });
    return query ? query.state.fetchStatus === 'fetching' : false;
  };

  return {
    getLoadingQueries,
    isAnyLoading,
    getLoadingByKey,
  };
};

// Error Handling Hook
export const useErrorHandler = () => {
  const handleApiError = (error: ApiError | Error) => {
    if (error instanceof Error) {
      console.error('API Error:', error.message);
      
      // Handle specific error types
      if ('statusCode' in error) {
        const apiError = error as unknown as ApiError;
        
        switch (apiError.statusCode) {
          case 401:
            // Handle unauthorized
            console.log('Unauthorized - redirecting to login');
            break;
          case 403:
            // Handle forbidden
            console.log('Forbidden - insufficient permissions');
            break;
          case 404:
            // Handle not found
            console.log('Resource not found');
            break;
          case 500:
            // Handle server error
            console.log('Server error - please try again later');
            break;
          default:
            console.log('Unknown error occurred');
        }
      }
    }
  };

  return { handleApiError };
};

// Cache Management Hook
export const useCacheManager = () => {
  const queryClient = useQueryClient();

  const clearCache = () => {
    queryClient.clear();
  };

  const clearCacheByKey = (queryKey: QueryKey) => {
    queryClient.removeQueries({ queryKey });
  };

  const prefetchQuery = <TData>(
    queryKey: QueryKey,
    queryFn: () => Promise<TData>,
    options?: { staleTime?: number }
  ) => {
    return queryClient.prefetchQuery({
      queryKey,
      queryFn,
      staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes default
    });
  };

  return {
    clearCache,
    clearCacheByKey,
    prefetchQuery,
    queryClient,
  };
};
