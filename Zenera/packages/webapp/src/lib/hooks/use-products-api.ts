/**
 * Products API Hooks với TanStack Query
 * Provides React hooks for product operations
 */

import { 
  useQuery, 
  useMutation, 
  useQueryClient,
  useInfiniteQuery,
  UseQueryOptions,
  UseMutationOptions,
  UseInfiniteQueryOptions
} from '@tanstack/react-query';
import { 
  productsApi, 
  Product, 
  ProductsQuery, 
  ProductsResponse, 
  CreateProductRequest, 
  UpdateProductRequest,
  Category 
} from '../api/products';
import { queryKeys } from './use-api';
import { ApiError } from '../api-client';

// Products Query Hooks
export const useProducts = (
  query: ProductsQuery = {},
  options?: UseQueryOptions<ProductsResponse, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.products.list(query),
    queryFn: () => productsApi.getProducts(query),
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

export const useProduct = (
  id: string,
  options?: UseQueryOptions<Product, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.products.detail(id),
    queryFn: () => productsApi.getProduct(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useCategories = (
  options?: UseQueryOptions<Category[], ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.products.categories(),
    queryFn: async () => {
      try {
        const result = await productsApi.getCategories();
        return result || []; // Ensure we always return an array
      } catch (error) {
        console.error('Error fetching categories:', error);
        return []; // Return empty array on error
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes (categories don't change often)
    ...options,
  });
};

export const useFeaturedProducts = (
  limit: number = 8,
  options?: UseQueryOptions<Product[], ApiError>
) => {
  return useQuery({
    queryKey: ['products', 'featured', limit],
    queryFn: async () => {
      const result = await productsApi.getFeaturedProducts(limit);
      return result || []; // Ensure we always return an array
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useRelatedProducts = (
  productId: string,
  limit: number = 4,
  options?: UseQueryOptions<Product[], ApiError>
) => {
  return useQuery({
    queryKey: ['products', 'related', productId, limit],
    queryFn: () => productsApi.getRelatedProducts(productId, limit),
    enabled: !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useProductsByCategory = (
  category: string,
  query: Omit<ProductsQuery, 'category'> = {},
  options?: UseQueryOptions<ProductsResponse, ApiError>
) => {
  return useQuery({
    queryKey: ['products', 'category', category, query],
    queryFn: () => productsApi.getProductsByCategory(category, query),
    enabled: !!category,
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

// Infinite Query for Products (for pagination)
export const useInfiniteProducts = (
  baseQuery: Omit<ProductsQuery, 'page'> = {},
  options?: UseInfiniteQueryOptions<ProductsResponse, ApiError>
) => {
  return useInfiniteQuery({
    queryKey: ['products', 'infinite', baseQuery],
    queryFn: ({ pageParam = 1 }) =>
      productsApi.getProducts({ ...baseQuery, page: pageParam as number }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (!lastPage.pagination) return undefined;
      const { page, totalPages } = lastPage.pagination;
      return page < totalPages ? page + 1 : undefined;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

// Search Hook với debouncing
export const useProductSearch = (
  searchTerm: string,
  filters: Omit<ProductsQuery, 'search'> = {},
  options?: UseQueryOptions<ProductsResponse, ApiError>
) => {
  return useQuery({
    queryKey: queryKeys.products.search(searchTerm),
    queryFn: () => productsApi.searchProducts(searchTerm, filters),
    enabled: searchTerm.length >= 2, // Only search if term is at least 2 characters
    staleTime: 1 * 60 * 1000, // 1 minute for search results
    ...options,
  });
};

// Products Mutation Hooks
export const useCreateProduct = (
  options?: UseMutationOptions<Product, ApiError, CreateProductRequest>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: productsApi.createProduct,
    onSuccess: (newProduct) => {
      // Invalidate products list to refetch with new product
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all });
      
      // Add new product to cache
      queryClient.setQueryData(
        queryKeys.products.detail(newProduct.id),
        newProduct
      );
      
      // Call custom success handler if provided
      options?.onSuccess?.(newProduct, {} as CreateProductRequest, undefined);
    },
    ...options,
  });
};

export const useUpdateProduct = (
  options?: UseMutationOptions<Product, ApiError, { id: string; data: UpdateProductRequest }>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }) => productsApi.updateProduct(id, data),
    onSuccess: (updatedProduct, variables) => {
      // Update product in cache
      queryClient.setQueryData(
        queryKeys.products.detail(variables.id),
        updatedProduct
      );
      
      // Invalidate products list to refetch
      queryClient.invalidateQueries({ queryKey: queryKeys.products.list() });
      
      // Call custom success handler if provided
      options?.onSuccess?.(updatedProduct, variables, undefined);
    },
    ...options,
  });
};

export const useDeleteProduct = (
  options?: UseMutationOptions<void, ApiError, string>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: productsApi.deleteProduct,
    onSuccess: (_, productId) => {
      // Remove product from cache
      queryClient.removeQueries({ queryKey: queryKeys.products.detail(productId) });
      
      // Invalidate products list to refetch
      queryClient.invalidateQueries({ queryKey: queryKeys.products.list() });
      
      // Call custom success handler if provided
      options?.onSuccess?.(undefined, productId, undefined);
    },
    ...options,
  });
};

// Utility Hooks
export const useProductActions = () => {
  const createMutation = useCreateProduct();
  const updateMutation = useUpdateProduct();
  const deleteMutation = useDeleteProduct();

  return {
    createProduct: createMutation.mutate,
    updateProduct: (id: string, data: UpdateProductRequest) => 
      updateMutation.mutate({ id, data }),
    deleteProduct: deleteMutation.mutate,
    
    // Loading states
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    
    // Error states
    createError: createMutation.error,
    updateError: updateMutation.error,
    deleteError: deleteMutation.error,
    
    // Reset functions
    resetCreateError: createMutation.reset,
    resetUpdateError: updateMutation.reset,
    resetDeleteError: deleteMutation.reset,
  };
};

// Prefetch utility
export const usePrefetchProduct = () => {
  const queryClient = useQueryClient();

  return (id: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.products.detail(id),
      queryFn: () => productsApi.getProduct(id),
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };
};
