/**
 * Permission Utilities
 * Extracted và adapted từ Medoo cho Zenera E-commerce
 */

// E-commerce Permission Constants
export const ZENERA_PERMISSIONS = {
  // Product Permissions
  PRODUCTS: {
    VIEW_ALL: 'products:view:all',
    VIEW_OWN: 'products:view:own',
    CREATE: 'products:create',
    UPDATE_ALL: 'products:update:all',
    UPDATE_OWN: 'products:update:own',
    DELETE_ALL: 'products:delete:all',
    DELETE_OWN: 'products:delete:own',
    MANAGE_INVENTORY: 'products:manage:inventory',
    APPROVE: 'products:approve',
  },

  // Order Permissions
  ORDERS: {
    VIEW_ALL: 'orders:view:all',
    VIEW_OWN: 'orders:view:own',
    CREATE: 'orders:create',
    UPDATE_ALL: 'orders:update:all',
    UPDATE_OWN: 'orders:update:own',
    UPDATE_STATUS: 'orders:update:status',
    CANCEL_ALL: 'orders:cancel:all',
    CANCEL_OWN: 'orders:cancel:own',
    REFUND: 'orders:refund',
  },

  // User Management Permissions
  USERS: {
    VIEW_ALL: 'users:view:all',
    VIEW_OWN: 'users:view:own',
    CREATE: 'users:create',
    UPDATE_ALL: 'users:update:all',
    UPDATE_OWN: 'users:update:own',
    DELETE_ALL: 'users:delete:all',
    MANAGE_ROLES: 'users:manage:roles',
    APPROVE_SELLERS: 'users:approve:sellers',
  },

  // Cart Permissions
  CART: {
    VIEW_OWN: 'cart:view:own',
    UPDATE_OWN: 'cart:update:own',
    CLEAR_OWN: 'cart:clear:own',
  },

  // Analytics Permissions
  ANALYTICS: {
    VIEW_ALL: 'analytics:view:all',
    VIEW_OWN: 'analytics:view:own',
    EXPORT: 'analytics:export',
  },

  // System Permissions
  SYSTEM: {
    ADMIN_PANEL: 'system:admin:panel',
    SETTINGS: 'system:settings',
    LOGS: 'system:logs',
    BACKUP: 'system:backup',
  },
} as const;

// Role-based Permission Sets (Match zen-buy.be structure)
const customerPermissions = [
  ZENERA_PERMISSIONS.PRODUCTS.VIEW_ALL,
  ZENERA_PERMISSIONS.ORDERS.VIEW_OWN,
  ZENERA_PERMISSIONS.ORDERS.CREATE,
  ZENERA_PERMISSIONS.ORDERS.CANCEL_OWN,
  ZENERA_PERMISSIONS.USERS.VIEW_OWN,
  ZENERA_PERMISSIONS.USERS.UPDATE_OWN,
  ZENERA_PERMISSIONS.CART.VIEW_OWN,
  ZENERA_PERMISSIONS.CART.UPDATE_OWN,
  ZENERA_PERMISSIONS.CART.CLEAR_OWN,
];

export const ROLE_PERMISSIONS = {
  // Match zen-buy.be Role enum
  customer: customerPermissions,

  seller: [
    // Customer permissions
    ...customerPermissions,
    // Seller-specific permissions (match zen-buy.be seller_info structure)
    ZENERA_PERMISSIONS.PRODUCTS.VIEW_OWN,
    ZENERA_PERMISSIONS.PRODUCTS.CREATE,
    ZENERA_PERMISSIONS.PRODUCTS.UPDATE_OWN,
    ZENERA_PERMISSIONS.PRODUCTS.DELETE_OWN,
    ZENERA_PERMISSIONS.PRODUCTS.MANAGE_INVENTORY,
    ZENERA_PERMISSIONS.ORDERS.VIEW_OWN,
    ZENERA_PERMISSIONS.ORDERS.UPDATE_STATUS,
    ZENERA_PERMISSIONS.ANALYTICS.VIEW_OWN,
  ],

  admin: [
    // All product permissions
    ...Object.values(ZENERA_PERMISSIONS.PRODUCTS),
    // All order permissions
    ...Object.values(ZENERA_PERMISSIONS.ORDERS),
    // All user permissions
    ...Object.values(ZENERA_PERMISSIONS.USERS),
    // All analytics permissions
    ...Object.values(ZENERA_PERMISSIONS.ANALYTICS),
    // All system permissions
    ...Object.values(ZENERA_PERMISSIONS.SYSTEM),
  ],

  super_admin: [
    // All permissions
    ...Object.values(ZENERA_PERMISSIONS.PRODUCTS),
    ...Object.values(ZENERA_PERMISSIONS.ORDERS),
    ...Object.values(ZENERA_PERMISSIONS.USERS),
    ...Object.values(ZENERA_PERMISSIONS.CART),
    ...Object.values(ZENERA_PERMISSIONS.ANALYTICS),
    ...Object.values(ZENERA_PERMISSIONS.SYSTEM),
  ],
} as const;

/**
 * Permission Validation Utilities
 * Adapted từ Medoo validPermissions
 */
export const PermissionUtils = {
  /**
   * Check if user has specific permission
   */
  hasPermission(permissionCode: string, userPermissions: string[] = []): boolean {
    if (!permissionCode) {
      return false;
    }
    return userPermissions.includes(permissionCode.trim());
  },

  /**
   * Check if user has any of the specified permissions
   */
  hasAnyPermission(permissionCodes: string[], userPermissions: string[] = []): boolean {
    if (!permissionCodes.length) {
      return false;
    }
    return permissionCodes.some(code => this.hasPermission(code, userPermissions));
  },

  /**
   * Check if user has all specified permissions
   */
  hasAllPermissions(permissionCodes: string[], userPermissions: string[] = []): boolean {
    if (!permissionCodes.length) {
      return false;
    }
    return permissionCodes.every(code => this.hasPermission(code, userPermissions));
  },

  /**
   * Get permissions by role
   */
  getPermissionsByRole(role: string): string[] {
    return [...(ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || [])];
  },

  /**
   * Check if user has role-based access
   */
  hasRoleAccess(userRoles: string[], requiredRoles: string[]): boolean {
    if (!requiredRoles.length) {
      return true;
    }
    return requiredRoles.some(role => userRoles.includes(role));
  },

  /**
   * Get user's effective permissions (từ roles + explicit permissions)
   */
  getEffectivePermissions(userRoles: string[] = [], explicitPermissions: string[] = []): string[] {
    const rolePermissions = userRoles.flatMap(role => this.getPermissionsByRole(role));
    const allPermissions = [...rolePermissions, ...explicitPermissions];
    
    // Remove duplicates
    return [...new Set(allPermissions)];
  },

  /**
   * Check if user can access resource
   */
  canAccessResource(
    resourcePermissions: string[],
    userRoles: string[] = [],
    userPermissions: string[] = []
  ): boolean {
    const effectivePermissions = this.getEffectivePermissions(userRoles, userPermissions);
    return this.hasAnyPermission(resourcePermissions, effectivePermissions);
  },

  /**
   * Generate CRUD permissions for a resource
   */
  generateCrudPermissions(resourceName: string): {
    view: string;
    create: string;
    update: string;
    delete: string;
  } {
    return {
      view: `${resourceName}:view`,
      create: `${resourceName}:create`,
      update: `${resourceName}:update`,
      delete: `${resourceName}:delete`,
    };
  },

  /**
   * Check if user is admin
   */
  isAdmin(userRoles: string[] = []): boolean {
    return userRoles.includes('admin') || userRoles.includes('super_admin');
  },

  /**
   * Check if user is seller
   */
  isSeller(userRoles: string[] = []): boolean {
    return userRoles.includes('seller');
  },

  /**
   * Check if user is customer
   */
  isCustomer(userRoles: string[] = []): boolean {
    return userRoles.includes('customer');
  },
};
