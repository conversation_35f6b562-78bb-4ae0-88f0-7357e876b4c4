import { createInstance, i18n, TFunction } from "i18next";
import resourcesToBackend from "i18next-resources-to-backend";
import { initReactI18next } from "react-i18next/initReactI18next";
import { getServerOptions } from "./settings";
import { DEFAULT_LOCALE } from "@/lib/constants/languages";

/**
 * Server-side translation support
 * Inspired by Medoo's server-side i18n
 */

export type ServerSideTranslationType = {
  t: TFunction;
  i18n: i18n;
};

/**
 * Initialize i18next instance for server-side rendering
 */
const initI18next = async (lng: string, ns: string | string[]): Promise<i18n> => {
  const i18nInstance = createInstance();
  
  await i18nInstance
    .use(initReactI18next)
    .use(
      resourcesToBackend((language: string, namespace: string) => {
        return import(`../../../public/locales/${language}/${namespace}.json`);
      })
    )
    .init(getServerOptions(lng as any, ns));
    
  return i18nInstance;
};

/**
 * Get server-side translation function
 */
export async function getServerTranslation(
  lng: string = DEFAULT_LOCALE,
  ns: string | string[] = "translation"
): Promise<ServerSideTranslationType> {
  const i18nInstance = await initI18next(lng, ns);
  
  return {
    t: i18nInstance.getFixedT(lng, Array.isArray(ns) ? ns[0] : ns),
    i18n: i18nInstance,
  };
}

/**
 * Get multiple namespace translations for server-side
 */
export async function getServerMultipleTranslations(
  lng: string = DEFAULT_LOCALE,
  namespaces: string[] = ["translation"]
): Promise<Record<string, TFunction>> {
  const i18nInstance = await initI18next(lng, namespaces);
  
  const translations: Record<string, TFunction> = {};
  
  namespaces.forEach(ns => {
    translations[ns] = i18nInstance.getFixedT(lng, ns);
  });
  
  return translations;
}

/**
 * Server-side translation with fallback
 */
export async function serverTranslate(
  key: string,
  lng: string = DEFAULT_LOCALE,
  ns: string = "translation",
  defaultValue?: string
): Promise<string> {
  try {
    const { t } = await getServerTranslation(lng, ns);
    return t(key, defaultValue || key);
  } catch (error) {
    console.warn(`Server translation failed for key: ${key}`, error);
    return defaultValue || key;
  }
}

/**
 * Batch server-side translations
 */
export async function serverBatchTranslate(
  keys: string[],
  lng: string = DEFAULT_LOCALE,
  ns: string = "translation"
): Promise<Record<string, string>> {
  try {
    const { t } = await getServerTranslation(lng, ns);
    const result: Record<string, string> = {};
    
    keys.forEach(key => {
      result[key] = t(key, key);
    });
    
    return result;
  } catch (error) {
    console.warn("Server batch translation failed", error);
    const result: Record<string, string> = {};
    keys.forEach(key => {
      result[key] = key;
    });
    return result;
  }
}

/**
 * Get server-side translation for specific page
 */
export async function getPageTranslations(
  lng: string = DEFAULT_LOCALE,
  pageNamespaces: string[] = ["translation", "common", "form"]
) {
  try {
    const translations = await getServerMultipleTranslations(lng, pageNamespaces);
    return translations;
  } catch (error) {
    console.warn("Failed to load page translations", error);
    return {};
  }
}

/**
 * Server-side translation utilities
 */
export const serverTranslationUtils = {
  getServerTranslation,
  getServerMultipleTranslations,
  serverTranslate,
  serverBatchTranslate,
  getPageTranslations,
};
