"use client";

import i18next from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import resourcesToBackend from "i18next-resources-to-backend";
import { getClientOptions } from "./settings";
import { Languages, DEFAULT_LOCALE, LOCALE_COOKIE_NAME } from "@/lib/constants/languages";

const runsOnServerSide = typeof window === "undefined";

/**
 * Initialize i18next for client-side
 * Inspired by Medoo's client-side i18n setup
 */
if (!runsOnServerSide && !i18next.isInitialized) {
  i18next
    .use(initReactI18next)
    .use(LanguageDetector)
    .use(
      resourcesToBackend((language: string, namespace: string) => {
        return import(`../../../public/locales/${language}/${namespace}.json`).catch((error) => {
          console.warn(`Failed to load translation file: ${language}/${namespace}.json`, error);
          // Return empty object as fallback
          return {};
        });
      })
    )
    .init({
      ...getClientOptions(),
      lng: undefined, // Let the language detector handle this
      detection: {
        order: ["path", "cookie", "localStorage", "navigator", "htmlTag"],
        lookupCookie: LOCALE_COOKIE_NAME,
        lookupLocalStorage: "zenera_locale",
        lookupFromPathIndex: 0,
        caches: ["localStorage", "cookie"],
        excludeCacheFor: ["cimode"],
      },
      fallbackLng: DEFAULT_LOCALE,
      debug: process.env.NODE_ENV === "development",
      interpolation: {
        escapeValue: false, // React already escapes
      },
      react: {
        useSuspense: false, // Disable suspense for better SSR compatibility
      },
    } as any);
}

/**
 * Get current language
 */
export const getCurrentLanguage = (): string => {
  if (runsOnServerSide) return DEFAULT_LOCALE;
  return i18next.language || DEFAULT_LOCALE;
};

/**
 * Change language
 */
export const changeLanguage = async (language: string): Promise<void> => {
  if (runsOnServerSide) return;
  
  if (!Object.values(Languages).includes(language as Languages)) {
    console.warn(`Invalid language: ${language}`);
    return;
  }

  await i18next.changeLanguage(language);
};

/**
 * Get translation function
 */
export const getTranslationFunction = (namespace: string = "translation") => {
  if (runsOnServerSide) {
    return (key: string, defaultValue?: string) => defaultValue || key;
  }
  
  return (key: string, defaultValue?: string, options?: any) => {
    return i18next.t(key, defaultValue || key, { ns: namespace, ...options });
  };
};

/**
 * Check if translation exists
 */
export const translationExists = (key: string, namespace: string = "translation"): boolean => {
  if (runsOnServerSide) return false;
  return i18next.exists(key, { ns: namespace });
};

/**
 * Get all loaded languages
 */
export const getLoadedLanguages = (): string[] => {
  if (runsOnServerSide) return [];
  return [...(i18next.languages || [])];
};

/**
 * Get resource data
 */
export const getResourceData = (language: string, namespace: string) => {
  if (runsOnServerSide) return {};
  return i18next.getResourceBundle(language, namespace) || {};
};

/**
 * Add resource bundle
 */
export const addResourceBundle = (
  language: string, 
  namespace: string, 
  resources: any, 
  deep: boolean = true, 
  overwrite: boolean = false
) => {
  if (runsOnServerSide) return;
  i18next.addResourceBundle(language, namespace, resources, deep, overwrite);
};

/**
 * Remove resource bundle
 */
export const removeResourceBundle = (language: string, namespace: string) => {
  if (runsOnServerSide) return;
  i18next.removeResourceBundle(language, namespace);
};

/**
 * Reload resources
 */
export const reloadResources = async (languages?: string[], namespaces?: string[]) => {
  if (runsOnServerSide) return;
  await i18next.reloadResources(languages, namespaces);
};

/**
 * Get direction (LTR/RTL)
 */
export const getDirection = (language?: string): "ltr" | "rtl" => {
  if (runsOnServerSide) return "ltr";
  return i18next.dir(language);
};

/**
 * Format interpolation
 */
export const formatInterpolation = (template: string, values: Record<string, any>): string => {
  if (runsOnServerSide) return template;
  return (i18next as any).format(template, values, getCurrentLanguage());
};

/**
 * Client-side utilities
 */
export const i18nClient = {
  getCurrentLanguage,
  changeLanguage,
  getTranslationFunction,
  translationExists,
  getLoadedLanguages,
  getResourceData,
  addResourceBundle,
  removeResourceBundle,
  reloadResources,
  getDirection,
  formatInterpolation,
  instance: i18next,
};

/**
 * Export i18next instance
 */
export default i18next;
