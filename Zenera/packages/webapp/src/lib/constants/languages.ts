/**
 * Language Constants
 * Inspired by Medoo's language system
 */

export enum Languages {
  EN = "en",
  VI = "vi", // Changed from VN to VI for consistency
}

export const LanguageValues = Object.values(Languages);

export const MAP_LANGUAGE_TEXT = {
  [Languages.EN]: "English",
  [Languages.VI]: "Tiếng Việt",
};

export const LANGUAGE_CONFIGS = {
  [Languages.EN]: {
    code: Languages.EN,
    name: "English",
    nativeName: "English",
    flag: "🇺🇸",
    rtl: false,
  },
  [Languages.VI]: {
    code: Languages.VI,
    name: "Vietnamese",
    nativeName: "Tiếng Việt",
    flag: "🇻🇳",
    rtl: false,
  },
};

export const languageOptions = Object.values(Languages).map(language => ({
  language,
  label: MAP_LANGUAGE_TEXT[language],
  value: language,
}));

export const getLanguageOptions = () => Object.values(Languages).map(language => ({
  value: language,
  label: MAP_LANGUAGE_TEXT[language],
}));

export type LanguageType = Languages.EN | Languages.VI;

/**
 * Get content by locale (for multi-language content)
 */
export const getContentByLocale = (
  multiLanguageContent: any,
  locale: string = Languages.EN
): string => {
  if (!multiLanguageContent) return "";
  
  if (typeof multiLanguageContent === "string") {
    return multiLanguageContent;
  }
  
  if (typeof multiLanguageContent === "object") {
    return multiLanguageContent[locale] || 
           multiLanguageContent[Languages.EN] || 
           multiLanguageContent[Languages.VI] || 
           "";
  }
  
  return "";
};

/**
 * Check if value has language properties
 */
export const hasLanguageProperties = (value: any): boolean => {
  if (value && typeof value === "object") {
    return !!(value?.vi || value?.en);
  }
  return false;
};

/**
 * Check if field is multiple language type
 */
export const checkIsMultipleLanguageType = (value: any): boolean => {
  return hasLanguageProperties(value);
};

/**
 * Create multi-language content object
 */
export const createMultiLanguageContent = (
  en: string = "",
  vi: string = ""
): Record<string, string> => {
  return {
    [Languages.EN]: en,
    [Languages.VI]: vi,
  };
};

/**
 * Validate multi-language content
 */
export const validateMultiLanguageContent = (
  content: any,
  requiredLanguages: Languages[] = [Languages.EN, Languages.VI]
): boolean => {
  if (!content || typeof content !== "object") return false;
  
  return requiredLanguages.every(lang => 
    content[lang] && typeof content[lang] === "string" && content[lang].trim().length > 0
  );
};

/**
 * Get missing languages in multi-language content
 */
export const getMissingLanguages = (
  content: any,
  requiredLanguages: Languages[] = [Languages.EN, Languages.VI]
): Languages[] => {
  if (!content || typeof content !== "object") return requiredLanguages;
  
  return requiredLanguages.filter(lang => 
    !content[lang] || typeof content[lang] !== "string" || content[lang].trim().length === 0
  );
};

/**
 * Default language fallback order
 */
export const LANGUAGE_FALLBACK_ORDER = [Languages.EN, Languages.VI];

/**
 * Get content with fallback
 */
export const getContentWithFallback = (
  multiLanguageContent: any,
  preferredLocale: string = Languages.EN
): string => {
  if (!multiLanguageContent) return "";
  
  if (typeof multiLanguageContent === "string") {
    return multiLanguageContent;
  }
  
  if (typeof multiLanguageContent === "object") {
    // Try preferred locale first
    if (multiLanguageContent[preferredLocale]) {
      return multiLanguageContent[preferredLocale];
    }
    
    // Try fallback order
    for (const fallbackLang of LANGUAGE_FALLBACK_ORDER) {
      if (multiLanguageContent[fallbackLang]) {
        return multiLanguageContent[fallbackLang];
      }
    }
    
    // Return first available value
    const values = Object.values(multiLanguageContent);
    return (values.find(val => typeof val === "string" && val.trim().length > 0) as string) || "";
  }
  
  return "";
};

/**
 * Cookie and storage keys
 */
export const LOCALE_COOKIE_NAME = "NEXT_LOCALE";
export const LOCALE_STORAGE_KEY = "zenera_locale";

/**
 * Validation functions
 */
export const isValidLanguage = (language: string): language is Languages => {
  return Object.values(Languages).includes(language as Languages);
};

/**
 * Default settings
 */
export const DEFAULT_LOCALE = Languages.EN;
export const SUPPORTED_LOCALES = Object.values(Languages);

/**
 * Language detection order
 */
export const LANGUAGE_DETECTION_ORDER = [
  "path",
  "cookie", 
  "localStorage",
  "navigator",
  "htmlTag"
];

/**
 * Namespace constants
 */
export const DEFAULT_NAMESPACE = "translation";
export const VALIDATION_NAMESPACE = "validation";
export const FORM_NAMESPACE = "form";
export const COMMON_NAMESPACE = "common";

export const DEFAULT_NAMESPACES = [
  DEFAULT_NAMESPACE,
  VALIDATION_NAMESPACE,
  FORM_NAMESPACE,
  COMMON_NAMESPACE,
];
