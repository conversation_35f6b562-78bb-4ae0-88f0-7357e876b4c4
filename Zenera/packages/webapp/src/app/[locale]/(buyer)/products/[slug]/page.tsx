import { Suspense } from 'react';
import { ProductDetailPage } from '@/components/buyer/products/product-detail-page';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { notFound } from 'next/navigation';

interface ProductDetailPageProps {
  params: Promise<{
    locale: string;
    slug: string;
  }>;
}

export default async function ProductDetail({ params }: ProductDetailPageProps) {
  const { locale, slug } = await params;
  
  // Mock product data - will be replaced with API call
  const mockProduct = {
    id: '1',
    name: 'Premium Wireless Headphones',
    slug: 'premium-wireless-headphones',
    description: 'Experience crystal-clear audio with our premium wireless headphones featuring noise cancellation and 30-hour battery life.',
    base_price: 299.99,
    sale_price: 249.99,
    images: [
      'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=800',
      'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=800',
      'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=800',
    ],
    category: 'Electronics',
    brand: 'AudioTech',
    rating: 4.5,
    reviews_count: 128,
    in_stock: true,
    stock_quantity: 15,
    sku: 'AT-WH-001',
    specifications: {
      'Battery Life': '30 hours',
      'Connectivity': 'Bluetooth 5.0, USB-C',
      'Weight': '250g',
      'Noise Cancellation': 'Active',
      'Warranty': '2 years',
    },
    variants: [
      { id: 'black', name: 'Black', price: 249.99, in_stock: true },
      { id: 'white', name: 'White', price: 249.99, in_stock: true },
      { id: 'blue', name: 'Blue', price: 269.99, in_stock: false },
    ],
  };

  // Check if product exists (in real app, this would be an API call)
  if (slug !== mockProduct.slug) {
    notFound();
  }
  
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <ProductDetailPage product={mockProduct} locale={locale} />
    </Suspense>
  );
}
