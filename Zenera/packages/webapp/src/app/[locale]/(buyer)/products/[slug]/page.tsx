import { Suspense } from 'react';
import { ProductDetailPage } from '@/components/buyer/products/product-detail';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { notFound } from 'next/navigation';

interface ProductDetailPageProps {
  params: Promise<{
    locale: string;
    slug: string;
  }>;
}

export default async function ProductDetail({ params }: ProductDetailPageProps) {
  const { locale, slug } = await params;
  
  // Mock product data - will be replaced with API call
  const mockProduct = {
    id: '1',
    name: 'Premium Wireless Headphones',
    slug: 'premium-wireless-headphones',
    description: 'Experience crystal-clear audio with our premium wireless headphones featuring noise cancellation and 30-hour battery life.',
    base_price: 299.99,
    sale_price: 249.99,
    images: [
      'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=800',
      'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=800',
      'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=800',
    ],
    category: 'Electronics',
    brand: 'AudioTech',
    rating: 4.5,
    reviews_count: 128,
    in_stock: true,
    stock_quantity: 15,
    sku: 'AT-WH-001',
    specifications: {
      'Battery Life': '30 hours',
      'Connectivity': 'Bluetooth 5.0, USB-C',
      'Weight': '250g',
      'Noise Cancellation': 'Active',
      'Warranty': '2 years',
    },
    variants: [
      { id: 'black', name: 'Black', price: 249.99, in_stock: true },
      { id: 'white', name: 'White', price: 249.99, in_stock: true },
      { id: 'blue', name: 'Blue', price: 269.99, in_stock: false },
    ],
  };

  // Mock related products data
  const mockRelatedProducts = [
    {
      id: '2',
      name: 'Wireless Gaming Mouse',
      slug: 'wireless-gaming-mouse',
      base_price: 79.99,
      sale_price: 59.99,
      images: ['https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=500'],
      category: 'Electronics',
      rating: 4.3,
      reviews_count: 89,
      in_stock: true,
    },
    {
      id: '3',
      name: 'Mechanical Keyboard',
      slug: 'mechanical-keyboard',
      base_price: 149.99,
      images: ['https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=500'],
      category: 'Electronics',
      rating: 4.7,
      reviews_count: 156,
      in_stock: true,
    },
    {
      id: '4',
      name: 'USB-C Hub',
      slug: 'usb-c-hub',
      base_price: 49.99,
      sale_price: 39.99,
      images: ['https://images.unsplash.com/photo-1625842268584-8f3296236761?w=500'],
      category: 'Electronics',
      rating: 4.1,
      reviews_count: 67,
      in_stock: true,
    },
    {
      id: '5',
      name: 'Bluetooth Speaker',
      slug: 'bluetooth-speaker',
      base_price: 89.99,
      images: ['https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=500'],
      category: 'Electronics',
      rating: 4.4,
      reviews_count: 203,
      in_stock: true,
    },
    {
      id: '6',
      name: 'Smartphone Stand',
      slug: 'smartphone-stand',
      base_price: 24.99,
      sale_price: 19.99,
      images: ['https://images.unsplash.com/photo-1556656793-08538906a9f8?w=500'],
      category: 'Electronics',
      rating: 4.0,
      reviews_count: 45,
      in_stock: true,
    },
  ];

  // Check if product exists (in real app, this would be an API call)
  if (slug !== mockProduct.slug) {
    notFound();
  }

  return (
    <Suspense fallback={<LoadingSpinner />}>
      <ProductDetailPage
        product={mockProduct}
        locale={locale}
        relatedProducts={mockRelatedProducts}
      />
    </Suspense>
  );
}
