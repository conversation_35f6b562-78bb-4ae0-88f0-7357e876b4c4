import { Suspense } from 'react';
import { ProductListPage } from '@/components/buyer/products/product-list-page';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface ProductsPageProps {
  params: Promise<{
    locale: string;
  }>;
  searchParams: Promise<{
    category?: string;
    sort?: string;
    page?: string;
    search?: string;
  }>;
}

export default async function ProductsPage({ params, searchParams }: ProductsPageProps) {
  const { locale } = await params;
  const filters = await searchParams;
  
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <ProductListPage locale={locale} filters={filters} />
    </Suspense>
  );
}
