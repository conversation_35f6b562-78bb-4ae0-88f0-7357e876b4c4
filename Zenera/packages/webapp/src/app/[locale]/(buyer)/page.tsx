import { Suspense } from 'react';
import { BuyerHomePage } from '@/components/buyer/home/<USER>';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface HomePageProps {
  params: Promise<{
    locale: string;
  }>;
}

export default async function HomePage({ params }: HomePageProps) {
  const { locale } = await params;

  return (
    <Suspense fallback={<LoadingSpinner />}>
      <BuyerHomePage locale={locale} />
    </Suspense>
  );
}
