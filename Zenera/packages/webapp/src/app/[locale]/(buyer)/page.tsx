import { Suspense } from 'react';
import { BuyerHomePage } from '@/components/buyer/home/<USER>';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface HomePageProps {
  params: {
    locale: string;
  };
}

export default function HomePage({ params: { locale } }: HomePageProps) {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <BuyerHomePage locale={locale} />
    </Suspense>
  );
}
