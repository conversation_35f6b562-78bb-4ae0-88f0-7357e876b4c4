import type { ReactNode } from 'react';
import { Navbar } from '@/components/buyer/layout/navbar';
import { Footer } from '@/components/buyer/layout/footer';

interface BuyerLayoutProps {
  children: ReactNode;
}

export default function BuyerLayout({ children }: BuyerLayoutProps) {
  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Navbar />
      <main className="flex-grow">{children}</main>
      <Footer />
    </div>
  );
}
