import { Suspense } from 'react';
import { ShopListPage } from '@/components/buyer/shops/shop-list-page';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface ShopsPageProps {
  params: Promise<{
    locale: string;
  }>;
  searchParams: Promise<{
    category?: string;
    sort?: string;
    page?: string;
    search?: string;
  }>;
}

export default async function ShopsPage({ params, searchParams }: ShopsPageProps) {
  const { locale } = await params;
  const filters = await searchParams;
  
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <ShopListPage locale={locale} filters={filters} />
    </Suspense>
  );
}
