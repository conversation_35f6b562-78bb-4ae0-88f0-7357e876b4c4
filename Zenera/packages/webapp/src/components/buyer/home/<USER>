"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { Star, Heart, ShoppingCart, Filter, Grid, List } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Image from 'next/image';
import { useProductsStore } from '@/stores/products-store';
import { useCartStore } from '@/stores/cart-store';
import type { Product } from '@zenera/sharing/types';

export function AllProducts() {
  const { t } = useZeneraTranslation('home');
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [displayCount, setDisplayCount] = useState(8);
  
  const { addItem } = useCartStore();

  // Mock products data - replace with actual API call
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 800));
        
        const mockProducts: Product[] = [
          {
            _id: '7',
            title: 'Bluetooth Speaker',
            description: 'Portable wireless speaker with excellent sound quality',
            price: 89.99,
            images: ['https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=400&h=300&fit=crop'],
            category: 'electronics',
            rating: 4.3,
            reviewCount: 78,
            inStock: true,
            stockQuantity: 40,
            seller: {
              _id: 'seller7',
              name: 'AudioTech',
              rating: 4.4,
              totalSales: 650
            },
            tags: ['new'],
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            _id: '8',
            title: 'Running Shoes',
            description: 'Comfortable running shoes for daily exercise',
            price: 129.99,
            originalPrice: 159.99,
            images: ['https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=300&fit=crop'],
            category: 'sports',
            rating: 4.6,
            reviewCount: 92,
            inStock: true,
            stockQuantity: 60,
            seller: {
              _id: 'seller8',
              name: 'SportGear',
              rating: 4.7,
              totalSales: 890
            },
            tags: ['bestseller'],
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            _id: '9',
            title: 'Coffee Maker',
            description: 'Automatic drip coffee maker with programmable timer',
            price: 79.99,
            images: ['https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=300&fit=crop'],
            category: 'home',
            rating: 4.2,
            reviewCount: 134,
            inStock: true,
            stockQuantity: 35,
            seller: {
              _id: 'seller9',
              name: 'KitchenPro',
              rating: 4.5,
              totalSales: 420
            },
            tags: [],
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            _id: '10',
            title: 'Desk Lamp',
            description: 'LED desk lamp with adjustable brightness and color temperature',
            price: 49.99,
            images: ['https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop'],
            category: 'home',
            rating: 4.4,
            reviewCount: 56,
            inStock: true,
            stockQuantity: 80,
            seller: {
              _id: 'seller10',
              name: 'LightingCo',
              rating: 4.3,
              totalSales: 290
            },
            tags: ['eco-friendly'],
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            _id: '11',
            title: 'Backpack',
            description: 'Durable travel backpack with multiple compartments',
            price: 69.99,
            originalPrice: 89.99,
            images: ['https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=300&fit=crop'],
            category: 'accessories',
            rating: 4.5,
            reviewCount: 87,
            inStock: true,
            stockQuantity: 45,
            seller: {
              _id: 'seller11',
              name: 'TravelGear',
              rating: 4.6,
              totalSales: 340
            },
            tags: ['travel'],
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            _id: '12',
            title: 'Smartphone Case',
            description: 'Protective case with wireless charging support',
            price: 24.99,
            images: ['https://images.unsplash.com/photo-1556656793-08538906a9f8?w=400&h=300&fit=crop'],
            category: 'accessories',
            rating: 4.1,
            reviewCount: 203,
            inStock: true,
            stockQuantity: 120,
            seller: {
              _id: 'seller12',
              name: 'MobileTech',
              rating: 4.2,
              totalSales: 780
            },
            tags: ['bestseller'],
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            _id: '13',
            title: 'Yoga Mat',
            description: 'Non-slip yoga mat for comfortable practice',
            price: 39.99,
            images: ['https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=300&fit=crop'],
            category: 'sports',
            rating: 4.7,
            reviewCount: 145,
            inStock: true,
            stockQuantity: 70,
            seller: {
              _id: 'seller13',
              name: 'FitnessPlus',
              rating: 4.8,
              totalSales: 560
            },
            tags: ['eco-friendly'],
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            _id: '14',
            title: 'Wireless Charger',
            description: 'Fast wireless charging pad for smartphones',
            price: 34.99,
            originalPrice: 49.99,
            images: ['https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop'],
            category: 'electronics',
            rating: 4.3,
            reviewCount: 89,
            inStock: true,
            stockQuantity: 55,
            seller: {
              _id: 'seller14',
              name: 'ChargeTech',
              rating: 4.4,
              totalSales: 450
            },
            tags: ['new'],
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ];
        
        setProducts(mockProducts);
      } catch (error) {
        console.error('Failed to fetch products:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  const sortedProducts = [...products].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'rating':
        return b.rating - a.rating;
      case 'newest':
      default:
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
  });

  const displayedProducts = sortedProducts.slice(0, displayCount);

  const handleProductClick = (productId: string) => {
    router.push(`/products/${productId}`);
  };

  const handleAddToCart = (product: Product, e: React.MouseEvent) => {
    e.stopPropagation();
    addItem({
      productId: product._id!,
      quantity: 1,
      price: product.price,
      product: product
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  if (isLoading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="bg-gray-200 rounded-lg h-80 animate-pulse"></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('allProducts.title')}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('allProducts.description')}
          </p>
        </div>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-center mb-8 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder={t('allProducts.sortBy')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">{t('allProducts.sort.newest')}</SelectItem>
                <SelectItem value="price-low">{t('allProducts.sort.priceLow')}</SelectItem>
                <SelectItem value="price-high">{t('allProducts.sort.priceHigh')}</SelectItem>
                <SelectItem value="rating">{t('allProducts.sort.rating')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'grid' | 'list')}>
            <TabsList>
              <TabsTrigger value="grid">
                <Grid className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger value="list">
                <List className="h-4 w-4" />
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Products Grid/List */}
        <div className={`grid gap-6 ${
          viewMode === 'grid' 
            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4' 
            : 'grid-cols-1'
        }`}>
          {displayedProducts.map((product) => (
            <Card
              key={product._id}
              className={`group cursor-pointer hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 overflow-hidden ${
                viewMode === 'list' ? 'flex flex-row' : ''
              }`}
              onClick={() => handleProductClick(product._id!)}
            >
              <CardContent className={`p-0 ${viewMode === 'list' ? 'flex-shrink-0 w-48' : ''}`}>
                <div className={`relative overflow-hidden ${viewMode === 'list' ? 'h-32' : 'h-48'}`}>
                  <Image
                    src={product.images[0] || '/images/placeholder-product.jpg'}
                    alt={product.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-300"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/images/placeholder-product.jpg';
                    }}
                  />
                  {product.originalPrice && product.originalPrice > product.price && (
                    <Badge className="absolute top-2 left-2 bg-red-500">
                      {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
                    </Badge>
                  )}
                  <Button
                    variant="outline"
                    size="icon"
                    className="absolute top-2 right-2 bg-white/80 hover:bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Add to wishlist functionality
                    }}
                  >
                    <Heart className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
              <CardFooter className={`p-4 space-y-3 ${viewMode === 'list' ? 'flex-1 flex flex-col justify-between' : ''}`}>
                <div className="w-full">
                  <h3 className="font-semibold text-gray-900 line-clamp-2 group-hover:text-primary transition-colors">
                    {product.title}
                  </h3>
                  {viewMode === 'list' && (
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">{product.description}</p>
                  )}
                  <div className="flex items-center space-x-1 mt-1">
                    {renderStars(product.rating)}
                    <span className="text-sm text-gray-500">({product.reviewCount})</span>
                  </div>
                  <div className="flex items-center justify-between mt-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg font-bold text-gray-900">${product.price}</span>
                      {product.originalPrice && product.originalPrice > product.price && (
                        <span className="text-sm text-gray-500 line-through">${product.originalPrice}</span>
                      )}
                    </div>
                    <Button
                      size="sm"
                      onClick={(e) => handleAddToCart(product, e)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    >
                      <ShoppingCart className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>

        {/* Load More Button */}
        {displayCount < products.length && (
          <div className="text-center mt-12">
            <Button
              variant="outline"
              size="lg"
              onClick={() => setDisplayCount(prev => prev + 8)}
              className="px-8"
            >
              {t('allProducts.loadMore')}
            </Button>
          </div>
        )}

        {/* View All Products Button */}
        <div className="text-center mt-8">
          <Button
            size="lg"
            onClick={() => router.push('/products')}
            className="px-8"
          >
            {t('allProducts.viewAll')}
          </Button>
        </div>
      </div>
    </section>
  );
}
