"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { Button } from '@/components/ui/button';
import { ArrowRight, Play } from 'lucide-react';

export function Hero() {
  const { t } = useZeneraTranslation('home');
  const router = useRouter();
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  const handleShopNow = () => {
    router.push('/products');
  };

  const handleLearnMore = () => {
    router.push('/about');
  };

  return (
    <section className="relative bg-gradient-to-r from-blue-900 via-blue-800 to-indigo-900 text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black/20">
        <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] opacity-10"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-24">
        <div className="grid md:grid-cols-2 gap-8 items-center">
          {/* Left Content */}
          <div className="space-y-6">
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                <span className="block">{t('hero.title.line1')}</span>
                <span className="block bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                  {t('hero.title.line2')}
                </span>
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 max-w-lg">
                {t('hero.description')}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                size="lg"
                className="bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white border-0 px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                onClick={handleShopNow}
              >
                {t('hero.shopNow')}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-white/30 text-white hover:bg-white/10 px-8 py-3 text-lg font-semibold backdrop-blur-sm"
                onClick={handleLearnMore}
              >
                {t('hero.learnMore')}
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 pt-8">
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-yellow-400">10K+</div>
                <div className="text-sm text-blue-200">{t('hero.stats.products')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-yellow-400">50K+</div>
                <div className="text-sm text-blue-200">{t('hero.stats.customers')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-yellow-400">99%</div>
                <div className="text-sm text-blue-200">{t('hero.stats.satisfaction')}</div>
              </div>
            </div>
          </div>

          {/* Right Content - Video/Image */}
          <div className="relative">
            <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20">
              {isVideoPlaying ? (
                <video
                  autoPlay
                  loop
                  muted
                  className="w-full h-64 md:h-80 lg:h-96 object-cover"
                  onLoadStart={() => setIsVideoPlaying(true)}
                >
                  <source src="/videos/hero-video.mp4" type="video/mp4" />
                  {/* Fallback image if video fails */}
                  <div className="w-full h-64 md:h-80 lg:h-96 bg-gradient-to-br from-blue-600 to-purple-700 flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Play className="h-8 w-8 text-white" />
                      </div>
                      <p className="text-white/80">{t('hero.videoUnavailable')}</p>
                    </div>
                  </div>
                </video>
              ) : (
                <div className="w-full h-64 md:h-80 lg:h-96 bg-gradient-to-br from-blue-600 to-purple-700 flex items-center justify-center cursor-pointer"
                     onClick={() => setIsVideoPlaying(true)}>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 hover:bg-white/30 transition-colors">
                      <Play className="h-8 w-8 text-white" />
                    </div>
                    <p className="text-white/80">{t('hero.playVideo')}</p>
                  </div>
                </div>
              )}
              
              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full opacity-20 blur-xl"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full opacity-20 blur-xl"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating elements */}
      <div className="absolute top-20 left-10 w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
      <div className="absolute top-40 right-20 w-3 h-3 bg-purple-400 rounded-full animate-pulse delay-1000"></div>
      <div className="absolute bottom-20 left-20 w-2 h-2 bg-pink-400 rounded-full animate-pulse delay-2000"></div>
    </section>
  );
}
