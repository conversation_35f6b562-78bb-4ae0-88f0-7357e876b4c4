"use client";

import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Image from 'next/image';

interface Category {
  id: string;
  name: string;
  image: string;
  productCount: number;
  href: string;
}

export function CategoryCarousel() {
  const { t } = useZeneraTranslation('home');
  const router = useRouter();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [categories, setCategories] = useState<Category[]>([]);

  // Mock categories data - replace with actual API call
  const mockCategories = useMemo(() => [
    {
      id: '1',
      name: t('categories.electronics'),
      image: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=300&fit=crop',
      productCount: 1250,
      href: '/categories/electronics'
    },
    {
      id: '2',
      name: t('categories.clothing'),
      image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=300&fit=crop',
      productCount: 890,
      href: '/categories/clothing'
    },
    {
      id: '3',
      name: t('categories.home'),
      image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
      productCount: 650,
      href: '/categories/home'
    },
    {
      id: '4',
      name: t('categories.books'),
      image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=300&fit=crop',
      productCount: 420,
      href: '/categories/books'
    },
    {
      id: '5',
      name: t('categories.sports'),
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
      productCount: 380,
      href: '/categories/sports'
    },
    {
      id: '6',
      name: t('categories.beauty'),
      image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=300&fit=crop',
      productCount: 290,
      href: '/categories/beauty'
    },
    {
      id: '7',
      name: t('categories.automotive'),
      image: 'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?w=400&h=300&fit=crop',
      productCount: 180,
      href: '/categories/automotive'
    },
    {
      id: '8',
      name: t('categories.toys'),
      image: 'https://images.unsplash.com/photo-1558877385-1c4b6e4b4b1a?w=400&h=300&fit=crop',
      productCount: 340,
      href: '/categories/toys'
    }
  ], [t]);

  useEffect(() => {
    setCategories(mockCategories);
  }, [mockCategories]);

  const itemsPerPage = 4;
  const maxIndex = Math.max(0, categories.length - itemsPerPage);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev >= maxIndex ? 0 : prev + 1));
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev <= 0 ? maxIndex : prev - 1));
  };

  const handleCategoryClick = (category: Category) => {
    router.push(category.href);
  };

  const visibleCategories = categories.slice(currentIndex, currentIndex + itemsPerPage);

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('categories.title')}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('categories.description')}
          </p>
        </div>

        <div className="relative">
          {/* Navigation Buttons */}
          <Button
            variant="outline"
            size="icon"
            className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 z-10 bg-white shadow-lg hover:shadow-xl"
            onClick={prevSlide}
            disabled={categories.length <= itemsPerPage}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 z-10 bg-white shadow-lg hover:shadow-xl"
            onClick={nextSlide}
            disabled={categories.length <= itemsPerPage}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          {/* Categories Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {visibleCategories.map((category) => (
              <Card
                key={category.id}
                className="group cursor-pointer hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 overflow-hidden"
                onClick={() => handleCategoryClick(category)}
              >
                <CardContent className="p-0">
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={category.image}
                      alt={category.name}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-300"
                      onError={(e) => {
                        // Fallback to gradient background if image fails
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        target.parentElement!.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                      }}
                    />
                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors duration-300"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center text-white">
                        <h3 className="text-xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300">
                          {category.name}
                        </h3>
                        <p className="text-sm opacity-90">
                          {category.productCount.toLocaleString()} {t('categories.products')}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Dots Indicator */}
          {categories.length > itemsPerPage && (
            <div className="flex justify-center mt-8 space-x-2">
              {Array.from({ length: maxIndex + 1 }).map((_, index) => (
                <button
                  key={index}
                  className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                    index === currentIndex ? 'bg-primary' : 'bg-gray-300'
                  }`}
                  onClick={() => setCurrentIndex(index)}
                />
              ))}
            </div>
          )}
        </div>

        {/* View All Categories Button */}
        <div className="text-center mt-12">
          <Button
            variant="outline"
            size="lg"
            onClick={() => router.push('/categories')}
            className="px-8"
          >
            {t('categories.viewAll')}
          </Button>
        </div>
      </div>
    </section>
  );
}
