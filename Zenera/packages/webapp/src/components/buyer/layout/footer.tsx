"use client";

import { Facebook, Twitter, Instagram, Youtube, Mail, Phone, MapPin } from 'lucide-react';
import Link from 'next/link';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';

export function Footer() {
  const { t } = useZeneraTranslation('footer');

  const quickLinks = [
    { href: '/', label: t('links.home') },
    { href: '/products', label: t('links.products') },
    { href: '/categories', label: t('links.categories') },
    { href: '/about', label: t('links.about') },
    { href: '/contact', label: t('links.contact') },
  ];

  const customerService = [
    { href: '/help', label: t('customerService.help') },
    { href: '/shipping', label: t('customerService.shipping') },
    { href: '/returns', label: t('customerService.returns') },
    { href: '/faq', label: t('customerService.faq') },
    { href: '/support', label: t('customerService.support') },
  ];

  const policies = [
    { href: '/privacy', label: t('policies.privacy') },
    { href: '/terms', label: t('policies.terms') },
    { href: '/cookies', label: t('policies.cookies') },
    { href: '/security', label: t('policies.security') },
  ];

  const socialLinks = [
    { href: '#', icon: Facebook, label: 'Facebook' },
    { href: '#', icon: Twitter, label: 'Twitter' },
    { href: '#', icon: Instagram, label: 'Instagram' },
    { href: '#', icon: Youtube, label: 'Youtube' },
  ];

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">Z</span>
              </div>
              <span className="ml-2 text-xl font-bold">Zenera</span>
            </div>
            <p className="text-gray-300 text-sm">
              {t('description')}
            </p>
            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-300">
                <MapPin className="h-4 w-4 mr-2" />
                {t('contact.address')}
              </div>
              <div className="flex items-center text-sm text-gray-300">
                <Phone className="h-4 w-4 mr-2" />
                {t('contact.phone')}
              </div>
              <div className="flex items-center text-sm text-gray-300">
                <Mail className="h-4 w-4 mr-2" />
                {t('contact.email')}
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{t('sections.quickLinks')}</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white text-sm transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Customer Service */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{t('sections.customerService')}</h3>
            <ul className="space-y-2">
              {customerService.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white text-sm transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Newsletter & Social */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{t('sections.newsletter')}</h3>
            <p className="text-gray-300 text-sm">
              {t('newsletter.description')}
            </p>
            <div className="flex space-x-2">
              <Input
                type="email"
                placeholder={t('newsletter.placeholder')}
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
              />
              <Button variant="default" size="sm">
                {t('newsletter.subscribe')}
              </Button>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">{t('sections.followUs')}</h4>
              <div className="flex space-x-3">
                {socialLinks.map((social) => {
                  const Icon = social.icon;
                  return (
                    <Link
                      key={social.label}
                      href={social.href}
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      <Icon className="h-5 w-5" />
                    </Link>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        <Separator className="my-8 bg-gray-700" />

        {/* Bottom section */}
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="text-sm text-gray-400">
            © 2025 Zenera. {t('copyright')}
          </div>
          <div className="flex space-x-6">
            {policies.map((policy) => (
              <Link
                key={policy.href}
                href={policy.href}
                className="text-sm text-gray-400 hover:text-white transition-colors"
              >
                {policy.label}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
}
