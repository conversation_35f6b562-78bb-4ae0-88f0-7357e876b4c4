"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { X, RotateCcw, CheckCircle, Star } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface ShopFiltersProps {
  onFilterChange: (filters: any) => void;
  currentFilters: any;
}

export function ShopFilters({ onFilterChange, currentFilters }: ShopFiltersProps) {
  const { t } = useZeneraTranslation('shops');
  const [ratingRange, setRatingRange] = useState([0, 5]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
  const [verifiedOnly, setVerifiedOnly] = useState(false);
  const [minProducts, setMinProducts] = useState(0);

  // Mock data - will be replaced with API calls
  const categories = [
    { id: 'electronics', name: 'Electronics', count: 156 },
    { id: 'fashion', name: 'Fashion', count: 89 },
    { id: 'home', name: 'Home & Garden', count: 67 },
    { id: 'sports', name: 'Sports & Outdoors', count: 45 },
    { id: 'books', name: 'Books & Media', count: 34 },
    { id: 'beauty', name: 'Beauty & Health', count: 78 },
  ];

  const locations = [
    { id: 'hcm', name: 'Ho Chi Minh City', count: 245 },
    { id: 'hanoi', name: 'Hanoi', count: 189 },
    { id: 'danang', name: 'Da Nang', count: 67 },
    { id: 'cantho', name: 'Can Tho', count: 45 },
    { id: 'haiphong', name: 'Hai Phong', count: 34 },
  ];

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    const newCategories = checked
      ? [...selectedCategories, categoryId]
      : selectedCategories.filter(id => id !== categoryId);
    
    setSelectedCategories(newCategories);
    onFilterChange({ categories: newCategories });
  };

  const handleLocationChange = (locationId: string, checked: boolean) => {
    const newLocations = checked
      ? [...selectedLocations, locationId]
      : selectedLocations.filter(id => id !== locationId);
    
    setSelectedLocations(newLocations);
    onFilterChange({ locations: newLocations });
  };

  const handleRatingChange = (value: number[]) => {
    setRatingRange(value);
    onFilterChange({ ratingMin: value[0], ratingMax: value[1] });
  };

  const handleVerifiedChange = (checked: boolean) => {
    setVerifiedOnly(checked);
    onFilterChange({ verified: checked });
  };

  const handleMinProductsChange = (value: number[]) => {
    setMinProducts(value[0]);
    onFilterChange({ minProducts: value[0] });
  };

  const clearAllFilters = () => {
    setSelectedCategories([]);
    setSelectedLocations([]);
    setRatingRange([0, 5]);
    setVerifiedOnly(false);
    setMinProducts(0);
    onFilterChange({});
  };

  const activeFiltersCount = selectedCategories.length + selectedLocations.length + 
    (verifiedOnly ? 1 : 0) + (minProducts > 0 ? 1 : 0);

  return (
    <div className="space-y-6">
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{t('filters.title')}</h3>
        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-red-600 hover:text-red-700"
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            {t('filters.clear')}
          </Button>
        )}
      </div>

      {/* Active Filters */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedCategories.map(categoryId => {
            const category = categories.find(c => c.id === categoryId);
            return (
              <Badge key={categoryId} variant="secondary" className="flex items-center gap-1">
                {category?.name}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleCategoryChange(categoryId, false)}
                />
              </Badge>
            );
          })}
          {selectedLocations.map(locationId => {
            const location = locations.find(l => l.id === locationId);
            return (
              <Badge key={locationId} variant="secondary" className="flex items-center gap-1">
                {location?.name}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleLocationChange(locationId, false)}
                />
              </Badge>
            );
          })}
          {verifiedOnly && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              {t('filters.verified')}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleVerifiedChange(false)}
              />
            </Badge>
          )}
          {minProducts > 0 && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {minProducts}+ {t('filters.products')}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleMinProductsChange([0])}
              />
            </Badge>
          )}
        </div>
      )}

      {/* Rating Range */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm flex items-center gap-2">
            <Star className="h-4 w-4 text-yellow-500" />
            {t('filters.rating')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Slider
            value={ratingRange}
            onValueChange={handleRatingChange}
            max={5}
            step={0.1}
            className="w-full"
          />
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>{ratingRange[0].toFixed(1)} ⭐</span>
            <span>{ratingRange[1].toFixed(1)} ⭐</span>
          </div>
        </CardContent>
      </Card>

      {/* Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">{t('filters.categories')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {categories.map((category) => (
            <div key={category.id} className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={category.id}
                  checked={selectedCategories.includes(category.id)}
                  onCheckedChange={(checked) => handleCategoryChange(category.id, checked as boolean)}
                />
                <Label htmlFor={category.id} className="text-sm font-normal cursor-pointer">
                  {category.name}
                </Label>
              </div>
              <span className="text-xs text-gray-500">({category.count})</span>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Locations */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">{t('filters.locations')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {locations.map((location) => (
            <div key={location.id} className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={location.id}
                  checked={selectedLocations.includes(location.id)}
                  onCheckedChange={(checked) => handleLocationChange(location.id, checked as boolean)}
                />
                <Label htmlFor={location.id} className="text-sm font-normal cursor-pointer">
                  {location.name}
                </Label>
              </div>
              <span className="text-xs text-gray-500">({location.count})</span>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Minimum Products */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">{t('filters.minProducts')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Slider
            value={[minProducts]}
            onValueChange={handleMinProductsChange}
            max={1000}
            step={50}
            className="w-full"
          />
          <div className="text-sm text-gray-600 text-center">
            {minProducts}+ {t('filters.products')}
          </div>
        </CardContent>
      </Card>

      {/* Verification Status */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">{t('filters.verification')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="verified"
              checked={verifiedOnly}
              onCheckedChange={handleVerifiedChange}
            />
            <Label htmlFor="verified" className="text-sm font-normal cursor-pointer flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              {t('filters.verified')}
            </Label>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
