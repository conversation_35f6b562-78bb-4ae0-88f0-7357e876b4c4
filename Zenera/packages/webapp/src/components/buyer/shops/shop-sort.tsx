"use client";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface ShopSortProps {
  onSortChange: (sort: string) => void;
  currentSort?: string;
}

export function ShopSort({ onSortChange, currentSort }: ShopSortProps) {
  const { t } = useZeneraTranslation('shops');

  const sortOptions = [
    { value: 'newest', label: t('sort.newest') },
    { value: 'oldest', label: t('sort.oldest') },
    { value: 'rating-high', label: t('sort.ratingHigh') },
    { value: 'rating-low', label: t('sort.ratingLow') },
    { value: 'products-high', label: t('sort.productsHigh') },
    { value: 'products-low', label: t('sort.productsLow') },
    { value: 'followers-high', label: t('sort.followersHigh') },
    { value: 'followers-low', label: t('sort.followersLow') },
    { value: 'name-asc', label: t('sort.nameAsc') },
    { value: 'name-desc', label: t('sort.nameDesc') },
  ];

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-600 whitespace-nowrap">
        {t('sort.label')}:
      </span>
      <Select value={currentSort} onValueChange={onSortChange}>
        <SelectTrigger className="w-48">
          <SelectValue placeholder={t('sort.placeholder')} />
        </SelectTrigger>
        <SelectContent>
          {sortOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
