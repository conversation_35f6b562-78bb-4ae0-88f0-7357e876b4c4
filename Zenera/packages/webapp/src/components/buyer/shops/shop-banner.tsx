"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Store, Users, Star, MapPin } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface ShopBannerProps {
  locale: string;
}

export function ShopBanner({ locale }: ShopBannerProps) {
  const { t } = useZeneraTranslation('shops');
  const [currentSlide, setCurrentSlide] = useState(0);

  const bannerSlides = [
    {
      id: 1,
      title: t('banner.slides.discover.title'),
      subtitle: t('banner.slides.discover.subtitle'),
      description: t('banner.slides.discover.description'),
      cta: t('banner.slides.discover.cta'),
      badge: t('banner.slides.discover.badge'),
      gradient: 'from-purple-600 via-blue-600 to-cyan-600',
      icon: Store,
      stats: { shops: '1,200+', products: '50,000+' }
    },
    {
      id: 2,
      title: t('banner.slides.verified.title'),
      subtitle: t('banner.slides.verified.subtitle'),
      description: t('banner.slides.verified.description'),
      cta: t('banner.slides.verified.cta'),
      badge: t('banner.slides.verified.badge'),
      gradient: 'from-emerald-500 via-teal-500 to-blue-500',
      icon: Star,
      stats: { verified: '95%', rating: '4.8+' }
    },
    {
      id: 3,
      title: t('banner.slides.community.title'),
      subtitle: t('banner.slides.community.subtitle'),
      description: t('banner.slides.community.description'),
      cta: t('banner.slides.community.cta'),
      badge: t('banner.slides.community.badge'),
      gradient: 'from-pink-500 via-purple-500 to-indigo-500',
      icon: Users,
      stats: { users: '100K+', reviews: '500K+' }
    },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % bannerSlides.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [bannerSlides.length]);

  const currentBanner = bannerSlides[currentSlide];
  const IconComponent = currentBanner.icon;

  return (
    <div className="relative overflow-hidden rounded-2xl mb-8 shadow-2xl">
      {/* Background with animated gradient */}
      <div className={`absolute inset-0 bg-gradient-to-r ${currentBanner.gradient} opacity-90`} />
      
      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] animate-pulse" />
      </div>

      {/* Floating elements */}
      <div className="absolute top-6 right-6 animate-bounce">
        <div className="w-4 h-4 bg-white rounded-full opacity-60" />
      </div>
      <div className="absolute top-16 right-16 animate-pulse delay-500">
        <div className="w-2 h-2 bg-white rounded-full opacity-40" />
      </div>
      <div className="absolute bottom-12 left-12 animate-bounce delay-1000">
        <div className="w-3 h-3 bg-white rounded-full opacity-30" />
      </div>

      {/* Content */}
      <div className="relative z-10 px-8 py-12 md:px-12 md:py-16">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
            {/* Left content */}
            <div className="lg:col-span-2 text-white">
              {/* Badge */}
              <Badge 
                className="mb-4 bg-white/20 text-white border-white/30 backdrop-blur-sm hover:bg-white/30 transition-all duration-300"
              >
                <IconComponent className="w-3 h-3 mr-1" />
                {currentBanner.badge}
              </Badge>

              {/* Title */}
              <h2 className="text-4xl md:text-5xl font-bold mb-4 leading-tight">
                <span className="block text-white/90 text-lg md:text-xl font-medium mb-2">
                  {currentBanner.subtitle}
                </span>
                <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                  {currentBanner.title}
                </span>
              </h2>

              {/* Description */}
              <p className="text-lg md:text-xl text-white/90 mb-6 leading-relaxed">
                {currentBanner.description}
              </p>

              {/* Stats */}
              <div className="flex gap-6 mb-6">
                {Object.entries(currentBanner.stats).map(([key, value]) => (
                  <div key={key} className="text-center">
                    <div className="text-2xl font-bold text-white">{value}</div>
                    <div className="text-sm text-white/80 capitalize">{t(`stats.${key}`)}</div>
                  </div>
                ))}
              </div>

              {/* CTA Button */}
              <Button 
                size="lg" 
                className="bg-white text-gray-900 hover:bg-white/90 shadow-lg hover:shadow-xl transition-all duration-300 group"
              >
                {currentBanner.cta}
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>

            {/* Right visual element */}
            <div className="flex justify-center lg:justify-end">
              <div className="relative">
                {/* Main icon */}
                <div className="w-40 h-40 md:w-48 md:h-48 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/20">
                  <IconComponent className="w-20 h-20 md:w-24 md:h-24 text-white animate-pulse" />
                </div>
                
                {/* Orbiting elements */}
                <div className="absolute -top-3 -right-3 w-8 h-8 bg-white/20 rounded-full animate-spin" />
                <div className="absolute -bottom-3 -left-3 w-6 h-6 bg-white/30 rounded-full animate-ping" />
                <div className="absolute top-1/2 -left-6 w-4 h-4 bg-white/25 rounded-full animate-bounce" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Slide indicators */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {bannerSlides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              index === currentSlide 
                ? 'bg-white w-8' 
                : 'bg-white/50 hover:bg-white/70'
            }`}
          />
        ))}
      </div>

      {/* Shine effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 animate-shine" />
    </div>
  );
}
