"use client";

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Heart, Star, MapPin, Users, Package, CheckCircle, Eye } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { cn } from '@/lib/utils';

interface Shop {
  id: string;
  name: string;
  slug: string;
  description: string;
  logo: string;
  banner: string;
  category: string;
  rating: number;
  reviews_count: number;
  products_count: number;
  followers_count: number;
  verified: boolean;
  location: string;
  established: string;
}

interface ShopGridProps {
  shops: Shop[];
  viewMode: 'grid' | 'list';
  locale: string;
}

export function ShopGrid({ shops, viewMode, locale }: ShopGridProps) {
  const { t } = useZeneraTranslation('shops');
  const [followedShops, setFollowedShops] = useState<Set<string>>(new Set());

  const toggleFollow = (shopId: string) => {
    const newFollowed = new Set(followedShops);
    if (newFollowed.has(shopId)) {
      newFollowed.delete(shopId);
    } else {
      newFollowed.add(shopId);
    }
    setFollowedShops(newFollowed);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          'h-4 w-4',
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        )}
      />
    ));
  };

  if (viewMode === 'list') {
    return (
      <div className="space-y-6">
        {shops.map((shop) => (
          <Card key={shop.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 hover-lift">
            <CardContent className="p-0">
              <div className="flex">
                {/* Shop Banner */}
                <div className="relative w-64 h-48 flex-shrink-0">
                  <Image
                    src={shop.banner}
                    alt={shop.name}
                    fill
                    className="object-cover"
                  />
                  {shop.verified && (
                    <Badge className="absolute top-3 left-3 bg-green-500 text-white">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      {t('badge.verified')}
                    </Badge>
                  )}
                </div>

                {/* Shop Info */}
                <div className="flex-1 p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-4">
                      {/* Shop Logo */}
                      <div className="relative w-16 h-16 rounded-full overflow-hidden border-4 border-white shadow-lg">
                        <Image
                          src={shop.logo}
                          alt={shop.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="text-xl font-bold text-gray-900">
                            <Link 
                              href={`/${locale}/shops/${shop.slug}`}
                              className="hover:text-blue-600 transition-colors"
                            >
                              {shop.name}
                            </Link>
                          </h3>
                          {shop.verified && (
                            <CheckCircle className="w-5 h-5 text-green-500" />
                          )}
                        </div>
                        
                        <Badge variant="secondary" className="mb-2">
                          {shop.category}
                        </Badge>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <MapPin className="w-4 h-4" />
                            {shop.location}
                          </div>
                          <span>•</span>
                          <span>{t('established')} {shop.established}</span>
                        </div>
                      </div>
                    </div>
                    
                    <Button
                      variant={followedShops.has(shop.id) ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => toggleFollow(shop.id)}
                      className="flex items-center gap-2"
                    >
                      <Heart className={cn(
                        'w-4 h-4',
                        followedShops.has(shop.id) && 'fill-current'
                      )} />
                      {followedShops.has(shop.id) ? t('actions.following') : t('actions.follow')}
                    </Button>
                  </div>

                  <p className="text-gray-600 mb-4 line-clamp-2">
                    {shop.description}
                  </p>

                  {/* Stats */}
                  <div className="grid grid-cols-4 gap-4 mb-4">
                    <div className="text-center">
                      <div className="flex items-center justify-center gap-1 mb-1">
                        {renderStars(shop.rating)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {shop.rating} ({formatNumber(shop.reviews_count)})
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Package className="w-4 h-4 text-blue-600" />
                      </div>
                      <div className="font-semibold text-gray-900">{formatNumber(shop.products_count)}</div>
                      <div className="text-xs text-gray-600">{t('stats.products')}</div>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Users className="w-4 h-4 text-purple-600" />
                      </div>
                      <div className="font-semibold text-gray-900">{formatNumber(shop.followers_count)}</div>
                      <div className="text-xs text-gray-600">{t('stats.followers')}</div>
                    </div>
                    <div className="text-center">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/${locale}/shops/${shop.slug}`}>
                          <Eye className="w-4 h-4 mr-1" />
                          {t('actions.visit')}
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Grid view
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {shops.map((shop) => (
        <Card key={shop.id} className="group overflow-hidden hover-lift border-0 shadow-md hover:shadow-2xl transition-all duration-300 bg-white rounded-xl">
          <CardContent className="p-0">
            {/* Shop Banner */}
            <div className="relative h-48 overflow-hidden">
              <Image
                src={shop.banner}
                alt={shop.name}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
              />
              {shop.verified && (
                <Badge className="absolute top-3 left-3 bg-green-500 text-white">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  {t('badge.verified')}
                </Badge>
              )}
              
              {/* Shop Logo Overlay */}
              <div className="absolute -bottom-8 left-4">
                <div className="relative w-16 h-16 rounded-full overflow-hidden border-4 border-white shadow-lg">
                  <Image
                    src={shop.logo}
                    alt={shop.name}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>

            {/* Shop Info */}
            <div className="pt-12 p-6">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-bold text-gray-900 text-lg">
                      <Link 
                        href={`/${locale}/shops/${shop.slug}`}
                        className="hover:text-transparent hover:bg-gradient-to-r hover:from-blue-600 hover:to-purple-600 hover:bg-clip-text transition-all duration-300"
                      >
                        {shop.name}
                      </Link>
                    </h3>
                    {shop.verified && (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    )}
                  </div>
                  
                  <Badge variant="secondary" className="mb-2 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-0">
                    {shop.category}
                  </Badge>
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleFollow(shop.id)}
                  className="text-gray-400 hover:text-red-500"
                >
                  <Heart className={cn(
                    'w-5 h-5',
                    followedShops.has(shop.id) && 'fill-current text-red-500'
                  )} />
                </Button>
              </div>

              <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                {shop.description}
              </p>

              {/* Location & Established */}
              <div className="flex items-center gap-2 text-xs text-gray-500 mb-4">
                <MapPin className="w-3 h-3" />
                <span>{shop.location}</span>
                <span>•</span>
                <span>{t('established')} {shop.established}</span>
              </div>

              {/* Rating */}
              <div className="flex items-center gap-2 mb-4">
                <div className="flex">
                  {renderStars(shop.rating)}
                </div>
                <span className="text-sm text-gray-600">
                  {shop.rating} ({formatNumber(shop.reviews_count)})
                </span>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4 text-center">
                <div>
                  <div className="flex items-center justify-center mb-1">
                    <Package className="w-4 h-4 text-blue-600" />
                  </div>
                  <div className="font-semibold text-gray-900">{formatNumber(shop.products_count)}</div>
                  <div className="text-xs text-gray-600">{t('stats.products')}</div>
                </div>
                <div>
                  <div className="flex items-center justify-center mb-1">
                    <Users className="w-4 h-4 text-purple-600" />
                  </div>
                  <div className="font-semibold text-gray-900">{formatNumber(shop.followers_count)}</div>
                  <div className="text-xs text-gray-600">{t('stats.followers')}</div>
                </div>
              </div>

              {/* Visit Shop Button */}
              <Button
                asChild
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                size="sm"
              >
                <Link href={`/${locale}/shops/${shop.slug}`}>
                  <Eye className="w-4 h-4 mr-2" />
                  {t('actions.visit')}
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
