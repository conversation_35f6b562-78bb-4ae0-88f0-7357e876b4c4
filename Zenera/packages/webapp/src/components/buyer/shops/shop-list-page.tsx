"use client";

import { useState } from 'react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { ShopGrid } from './shop-grid';
import { ShopFilters } from './shop-filters';
import { ShopSort } from './shop-sort';
import { ShopSearch } from './shop-search';
import { ShopBanner } from './shop-banner';
import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Grid, List, Filter } from 'lucide-react';

interface ShopListPageProps {
  locale: string;
  filters?: {
    category?: string;
    sort?: string;
    page?: string;
    search?: string;
  };
}

export function ShopListPage({ locale, filters = {} }: ShopListPageProps) {
  const { t } = useZeneraTranslation('shops');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [currentFilters, setCurrentFilters] = useState(filters);

  // Mock data - will be replaced with API calls
  const mockShops = [
    {
      id: '1',
      name: 'TechWorld Store',
      slug: 'techworld-store',
      description: 'Your one-stop destination for the latest technology and gadgets',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200',
      banner: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',
      category: 'Electronics',
      rating: 4.8,
      reviews_count: 1250,
      products_count: 450,
      followers_count: 15600,
      verified: true,
      location: 'Ho Chi Minh City',
      established: '2018',
    },
    {
      id: '2',
      name: 'Fashion Hub',
      slug: 'fashion-hub',
      description: 'Trendy fashion for modern lifestyle',
      logo: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200',
      banner: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',
      category: 'Fashion',
      rating: 4.6,
      reviews_count: 890,
      products_count: 320,
      followers_count: 8900,
      verified: true,
      location: 'Hanoi',
      established: '2020',
    },
    // Add more mock shops as needed
  ];

  const breadcrumbItems = [
    { label: t('breadcrumb.home'), href: `/${locale}` },
    { label: t('breadcrumb.shops'), href: `/${locale}/shops` },
  ];

  const handleFilterChange = (newFilters: any) => {
    setCurrentFilters({ ...currentFilters, ...newFilters });
  };

  const handleSortChange = (sort: string) => {
    setCurrentFilters({ ...currentFilters, sort });
  };

  const handleSearchChange = (search: string) => {
    setCurrentFilters({ ...currentFilters, search });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <SimpleBreadcrumb items={breadcrumbItems} className="mb-6" />

        {/* Page Header with Banner */}
        <ShopBanner locale={locale} />
        
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4 gradient-text">
            {t('title')}
          </h1>
          <p className="text-gray-600 text-lg">
            {t('description')}
          </p>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <ShopSearch 
            onSearch={handleSearchChange}
            initialValue={currentFilters.search}
          />
        </div>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="sm:hidden"
            >
              <Filter className="h-4 w-4 mr-2" />
              {t('filters.toggle')}
            </Button>
            
            <div className="text-sm text-gray-600">
              {t('results.showing', { count: mockShops.length })}
            </div>
          </div>

          <div className="flex items-center gap-4">
            <ShopSort 
              onSortChange={handleSortChange}
              currentSort={currentFilters.sort}
            />
            
            <div className="flex items-center border rounded-lg">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-r-none"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex gap-8">
          {/* Filters Sidebar */}
          <div className={`${showFilters ? 'block' : 'hidden'} sm:block w-full sm:w-64 flex-shrink-0`}>
            <ShopFilters 
              onFilterChange={handleFilterChange}
              currentFilters={currentFilters}
            />
          </div>

          {/* Shops Grid */}
          <div className="flex-1">
            <ShopGrid 
              shops={mockShops}
              viewMode={viewMode}
              locale={locale}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
