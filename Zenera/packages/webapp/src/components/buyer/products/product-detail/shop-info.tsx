"use client";

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Heart, 
  Star, 
  MapPin, 
  Users, 
  Package, 
  CheckCircle, 
  MessageCircle,
  ExternalLink
} from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { cn } from '@/lib/utils';

interface Shop {
  id: string;
  name: string;
  slug: string;
  description: string;
  logo: string;
  banner: string;
  category: string;
  rating: number;
  reviews_count: number;
  products_count: number;
  followers_count: number;
  verified: boolean;
  location: string;
  established: string;
}

interface ShopInfoProps {
  shop: Shop;
  locale: string;
}

export function ShopInfo({ shop, locale }: ShopInfoProps) {
  const { t } = useZeneraTranslation('product-detail');
  const [isFollowing, setIsFollowing] = useState(false);

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          'h-4 w-4',
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        )}
      />
    ));
  };

  return (
    <Card className="mt-8 overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
      <CardContent className="p-0">
        {/* Shop Banner */}
        <div className="relative h-32 bg-gradient-to-r from-blue-600 to-purple-600">
          <Image
            src={shop.banner}
            alt={shop.name}
            fill
            className="object-cover opacity-80"
          />
          {shop.verified && (
            <Badge className="absolute top-4 right-4 bg-green-500 text-white">
              <CheckCircle className="w-3 h-3 mr-1" />
              {t('shop.verified')}
            </Badge>
          )}
        </div>

        <div className="p-6">
          <div className="flex items-start gap-4 mb-4">
            {/* Shop Logo */}
            <div className="relative w-16 h-16 rounded-full overflow-hidden border-4 border-white shadow-lg -mt-8">
              <Image
                src={shop.logo}
                alt={shop.name}
                fill
                className="object-cover"
              />
            </div>

            <div className="flex-1">
              <div className="flex items-center justify-between mb-2">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-xl font-bold text-gray-900">
                      <Link 
                        href={`/${locale}/shops/${shop.slug}`}
                        className="hover:text-blue-600 transition-colors"
                      >
                        {shop.name}
                      </Link>
                    </h3>
                    {shop.verified && (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    )}
                  </div>
                  
                  <Badge variant="secondary" className="mb-2 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-0">
                    {shop.category}
                  </Badge>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsFollowing(!isFollowing)}
                    className={cn(
                      "transition-all duration-300",
                      isFollowing && "bg-blue-50 border-blue-200 text-blue-700"
                    )}
                  >
                    <Heart className={cn(
                      'w-4 h-4 mr-1',
                      isFollowing && 'fill-current text-blue-600'
                    )} />
                    {isFollowing ? t('shop.following') : t('shop.follow')}
                  </Button>
                  
                  <Button variant="outline" size="sm">
                    <MessageCircle className="w-4 h-4 mr-1" />
                    {t('shop.chat')}
                  </Button>
                </div>
              </div>

              <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                {shop.description}
              </p>

              <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4" />
                  {shop.location}
                </div>
                <span>•</span>
                <span>{t('shop.established')} {shop.established}</span>
              </div>

              {/* Rating */}
              <div className="flex items-center gap-2 mb-4">
                <div className="flex">
                  {renderStars(shop.rating)}
                </div>
                <span className="text-sm text-gray-600">
                  {shop.rating} ({formatNumber(shop.reviews_count)} {t('reviews')})
                </span>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Package className="w-4 h-4 text-blue-600" />
                  </div>
                  <div className="font-semibold text-gray-900">{formatNumber(shop.products_count)}</div>
                  <div className="text-xs text-gray-600">{t('shop.products')}</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Users className="w-4 h-4 text-purple-600" />
                  </div>
                  <div className="font-semibold text-gray-900">{formatNumber(shop.followers_count)}</div>
                  <div className="text-xs text-gray-600">{t('shop.followers')}</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <Button 
                    asChild
                    variant="outline" 
                    size="sm"
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
                  >
                    <Link href={`/${locale}/shops/${shop.slug}`}>
                      <ExternalLink className="w-4 h-4 mr-1" />
                      {t('shop.visit')}
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
