"use client";

import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface ProductTabsProps {
  specifications: Record<string, string>;
}

export function ProductTabs({ specifications }: ProductTabsProps) {
  const { t } = useZeneraTranslation('product-detail');

  return (
    <Card className="shadow-lg">
      <CardContent className="p-6">
        <Tabs defaultValue="specifications" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="specifications">{t('tabs.specifications')}</TabsTrigger>
            <TabsTrigger value="reviews">{t('tabs.reviews')}</TabsTrigger>
            <TabsTrigger value="shipping">{t('tabs.shipping')}</TabsTrigger>
          </TabsList>
          
          <TabsContent value="specifications" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(specifications).map(([key, value]) => (
                <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                  <span className="font-medium text-gray-700">{key}:</span>
                  <span className="text-gray-600">{value}</span>
                </div>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="reviews" className="mt-6">
            <div className="text-center py-8 text-gray-500">
              {t('tabs.reviewsContent')}
            </div>
          </TabsContent>
          
          <TabsContent value="shipping" className="mt-6">
            <div className="space-y-4 text-gray-600">
              <p>{t('tabs.shippingContent')}</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
