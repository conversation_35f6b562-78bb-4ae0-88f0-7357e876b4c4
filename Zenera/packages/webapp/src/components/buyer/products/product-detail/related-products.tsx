"use client";

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Heart, ShoppingCart, Star, ChevronLeft, ChevronRight } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { useCartStore } from '@/stores/cart-store';
import { cn } from '@/lib/utils';

interface RelatedProduct {
  id: string;
  name: string;
  slug: string;
  base_price: number;
  sale_price?: number;
  images: string[];
  category: string;
  rating: number;
  reviews_count: number;
  in_stock: boolean;
}

interface RelatedProductsProps {
  products: RelatedProduct[];
  locale: string;
  title?: string;
}

export function RelatedProducts({ products, locale, title }: RelatedProductsProps) {
  const { t } = useZeneraTranslation('product-detail');
  const { addItem } = useCartStore();
  const [wishlist, setWishlist] = useState<Set<string>>(new Set());
  const [currentIndex, setCurrentIndex] = useState(0);

  const itemsPerPage = 4;
  const maxIndex = Math.max(0, products.length - itemsPerPage);

  const toggleWishlist = (productId: string) => {
    const newWishlist = new Set(wishlist);
    if (newWishlist.has(productId)) {
      newWishlist.delete(productId);
    } else {
      newWishlist.add(productId);
    }
    setWishlist(newWishlist);
  };

  const handleAddToCart = async (product: RelatedProduct) => {
    try {
      await addItem(product as any);
      // TODO: Show success toast
    } catch (error) {
      console.error('Failed to add to cart:', error);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat(locale === 'vi' ? 'vi-VN' : 'en-US', {
      style: 'currency',
      currency: locale === 'vi' ? 'VND' : 'USD',
    }).format(price);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          'h-3 w-3',
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        )}
      />
    ));
  };

  const nextSlide = () => {
    setCurrentIndex(Math.min(currentIndex + 1, maxIndex));
  };

  const prevSlide = () => {
    setCurrentIndex(Math.max(currentIndex - 1, 0));
  };

  if (products.length === 0) return null;

  return (
    <div className="mt-16">
      <div className="flex items-center justify-between mb-8">
        <h2 className="text-2xl font-bold text-gray-900">
          {title || t('relatedProducts.title')}
        </h2>
        
        {products.length > itemsPerPage && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={prevSlide}
              disabled={currentIndex === 0}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={nextSlide}
              disabled={currentIndex >= maxIndex}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <div className="relative overflow-hidden">
        <div 
          className="flex transition-transform duration-300 ease-in-out gap-6"
          style={{ transform: `translateX(-${currentIndex * (100 / itemsPerPage)}%)` }}
        >
          {products.map((product) => (
            <div key={product.id} className="flex-shrink-0 w-1/4 min-w-[280px]">
              <Card className="group overflow-hidden hover-lift border-0 shadow-md hover:shadow-xl transition-all duration-300 bg-white rounded-xl">
                <CardContent className="p-0">
                  {/* Product Image */}
                  <div className="relative aspect-square overflow-hidden rounded-t-xl">
                    <Image
                      src={product.images[0]}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    {product.sale_price && (
                      <Badge className="absolute top-3 left-3 bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg">
                        {t('badge.sale')}
                      </Badge>
                    )}
                    
                    {/* Hover Actions */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          className="glass text-white border-white/30 hover:bg-white/20 backdrop-blur-sm"
                          onClick={() => toggleWishlist(product.id)}
                        >
                          <Heart 
                            className={cn(
                              'h-4 w-4',
                              wishlist.has(product.id) && 'fill-current text-red-400'
                            )}
                          />
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Product Info */}
                  <div className="p-4">
                    <Badge variant="secondary" className="mb-2 text-xs bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-0">
                      {product.category}
                    </Badge>
                    
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 text-sm">
                      <Link 
                        href={`/${locale}/products/${product.slug}`}
                        className="hover:text-transparent hover:bg-gradient-to-r hover:from-blue-600 hover:to-purple-600 hover:bg-clip-text transition-all duration-300"
                      >
                        {product.name}
                      </Link>
                    </h3>

                    {/* Rating */}
                    <div className="flex items-center gap-1 mb-2">
                      <div className="flex">
                        {renderStars(product.rating)}
                      </div>
                      <span className="text-xs text-gray-600">
                        ({product.reviews_count})
                      </span>
                    </div>

                    {/* Price */}
                    <div className="flex items-center gap-2 mb-3">
                      <span className="font-bold text-gray-900 text-sm">
                        {formatPrice(product.sale_price || product.base_price)}
                      </span>
                      {product.sale_price && (
                        <span className="text-xs text-gray-500 line-through">
                          {formatPrice(product.base_price)}
                        </span>
                      )}
                    </div>

                    {/* Add to Cart Button */}
                    <Button
                      onClick={() => handleAddToCart(product)}
                      disabled={!product.in_stock}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                      size="sm"
                    >
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      {product.in_stock ? t('addToCart') : t('outOfStock')}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
