"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowR<PERSON>, Sparkles, Zap, Star } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface ProductBannerProps {
  locale: string;
}

export function ProductBanner({ locale }: ProductBannerProps) {
  const { t } = useZeneraTranslation('products');
  const [currentSlide, setCurrentSlide] = useState(0);

  const bannerSlides = [
    {
      id: 1,
      title: t('banner.slides.newCollection.title'),
      subtitle: t('banner.slides.newCollection.subtitle'),
      description: t('banner.slides.newCollection.description'),
      cta: t('banner.slides.newCollection.cta'),
      badge: t('banner.slides.newCollection.badge'),
      gradient: 'from-blue-600 via-purple-600 to-pink-600',
      icon: Sparkles,
    },
    {
      id: 2,
      title: t('banner.slides.flashSale.title'),
      subtitle: t('banner.slides.flashSale.subtitle'),
      description: t('banner.slides.flashSale.description'),
      cta: t('banner.slides.flashSale.cta'),
      badge: t('banner.slides.flashSale.badge'),
      gradient: 'from-orange-500 via-red-500 to-pink-500',
      icon: Zap,
    },
    {
      id: 3,
      title: t('banner.slides.premium.title'),
      subtitle: t('banner.slides.premium.subtitle'),
      description: t('banner.slides.premium.description'),
      cta: t('banner.slides.premium.cta'),
      badge: t('banner.slides.premium.badge'),
      gradient: 'from-emerald-500 via-teal-500 to-cyan-500',
      icon: Star,
    },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % bannerSlides.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [bannerSlides.length]);

  const currentBanner = bannerSlides[currentSlide];
  const IconComponent = currentBanner.icon;

  return (
    <div className="relative overflow-hidden rounded-2xl mb-8 shadow-2xl">
      {/* Background with animated gradient */}
      <div className={`absolute inset-0 bg-gradient-to-r ${currentBanner.gradient} opacity-90`} />
      
      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] animate-pulse" />
      </div>

      {/* Floating elements */}
      <div className="absolute top-4 right-4 animate-bounce">
        <div className="w-3 h-3 bg-white rounded-full opacity-60" />
      </div>
      <div className="absolute top-12 right-12 animate-pulse">
        <div className="w-2 h-2 bg-white rounded-full opacity-40" />
      </div>
      <div className="absolute bottom-8 left-8 animate-bounce delay-1000">
        <div className="w-4 h-4 bg-white rounded-full opacity-30" />
      </div>

      {/* Content */}
      <div className="relative z-10 px-8 py-12 md:px-12 md:py-16">
        <div className="max-w-4xl mx-auto">
          <div className="flex flex-col md:flex-row items-center gap-8">
            {/* Left content */}
            <div className="flex-1 text-white">
              {/* Badge */}
              <Badge 
                className="mb-4 bg-white/20 text-white border-white/30 backdrop-blur-sm hover:bg-white/30 transition-all duration-300"
              >
                <IconComponent className="w-3 h-3 mr-1" />
                {currentBanner.badge}
              </Badge>

              {/* Title */}
              <h2 className="text-4xl md:text-5xl font-bold mb-4 leading-tight">
                <span className="block text-white/90 text-lg md:text-xl font-medium mb-2">
                  {currentBanner.subtitle}
                </span>
                <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                  {currentBanner.title}
                </span>
              </h2>

              {/* Description */}
              <p className="text-lg md:text-xl text-white/90 mb-6 leading-relaxed">
                {currentBanner.description}
              </p>

              {/* CTA Button */}
              <Button 
                size="lg" 
                className="bg-white text-gray-900 hover:bg-white/90 shadow-lg hover:shadow-xl transition-all duration-300 group"
              >
                {currentBanner.cta}
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>

            {/* Right visual element */}
            <div className="flex-shrink-0">
              <div className="relative">
                {/* Main icon */}
                <div className="w-32 h-32 md:w-40 md:h-40 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/20">
                  <IconComponent className="w-16 h-16 md:w-20 md:h-20 text-white animate-pulse" />
                </div>
                
                {/* Orbiting elements */}
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-white/20 rounded-full animate-spin" />
                <div className="absolute -bottom-2 -left-2 w-4 h-4 bg-white/30 rounded-full animate-ping" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Slide indicators */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {bannerSlides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              index === currentSlide 
                ? 'bg-white w-8' 
                : 'bg-white/50 hover:bg-white/70'
            }`}
          />
        ))}
      </div>

      {/* Shine effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 animate-shine" />
    </div>
  );
}
