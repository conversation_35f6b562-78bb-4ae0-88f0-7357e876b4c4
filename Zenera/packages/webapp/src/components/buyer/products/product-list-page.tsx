"use client";

import { useState, useEffect } from 'react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { ProductGrid } from './product-grid';
import { ProductFilters } from './product-filters';
import { ProductSort } from './product-sort';
import { ProductSearch } from './product-search';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Grid, List, Filter } from 'lucide-react';

interface ProductListPageProps {
  locale: string;
  filters?: {
    category?: string;
    sort?: string;
    page?: string;
    search?: string;
  };
}

export function ProductListPage({ locale, filters = {} }: ProductListPageProps) {
  const { t } = useZeneraTranslation('products');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [currentFilters, setCurrentFilters] = useState(filters);

  // Mock data - will be replaced with API calls
  const mockProducts = [
    {
      id: '1',
      name: 'Premium Wireless Headphones',
      slug: 'premium-wireless-headphones',
      base_price: 299.99,
      sale_price: 249.99,
      images: ['https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500'],
      category: 'Electronics',
      rating: 4.5,
      reviews_count: 128,
      in_stock: true,
    },
    {
      id: '2', 
      name: 'Organic Cotton T-Shirt',
      slug: 'organic-cotton-t-shirt',
      base_price: 29.99,
      images: ['https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500'],
      category: 'Fashion',
      rating: 4.2,
      reviews_count: 89,
      in_stock: true,
    },
    // Add more mock products as needed
  ];

  const breadcrumbItems = [
    { label: t('breadcrumb.home'), href: `/${locale}` },
    { label: t('breadcrumb.products'), href: `/${locale}/products` },
  ];

  const handleFilterChange = (newFilters: any) => {
    setCurrentFilters({ ...currentFilters, ...newFilters });
  };

  const handleSortChange = (sort: string) => {
    setCurrentFilters({ ...currentFilters, sort });
  };

  const handleSearchChange = (search: string) => {
    setCurrentFilters({ ...currentFilters, search });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <Breadcrumb items={breadcrumbItems} className="mb-6" />

        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {t('title')}
          </h1>
          <p className="text-gray-600">
            {t('description')}
          </p>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <ProductSearch 
            onSearch={handleSearchChange}
            initialValue={currentFilters.search}
          />
        </div>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="sm:hidden"
            >
              <Filter className="h-4 w-4 mr-2" />
              {t('filters.toggle')}
            </Button>
            
            <div className="text-sm text-gray-600">
              {t('results.showing', { count: mockProducts.length })}
            </div>
          </div>

          <div className="flex items-center gap-4">
            <ProductSort 
              onSortChange={handleSortChange}
              currentSort={currentFilters.sort}
            />
            
            <div className="flex items-center border rounded-lg">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-r-none"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex gap-8">
          {/* Filters Sidebar */}
          <div className={`${showFilters ? 'block' : 'hidden'} sm:block w-full sm:w-64 flex-shrink-0`}>
            <ProductFilters 
              onFilterChange={handleFilterChange}
              currentFilters={currentFilters}
            />
          </div>

          {/* Products Grid */}
          <div className="flex-1">
            <ProductGrid 
              products={mockProducts}
              viewMode={viewMode}
              locale={locale}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
