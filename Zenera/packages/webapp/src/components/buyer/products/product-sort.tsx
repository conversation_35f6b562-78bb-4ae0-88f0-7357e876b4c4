"use client";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface ProductSortProps {
  onSortChange: (sort: string) => void;
  currentSort?: string;
}

export function ProductSort({ onSortChange, currentSort }: ProductSortProps) {
  const { t } = useZeneraTranslation('products');

  const sortOptions = [
    { value: 'newest', label: t('sort.newest') },
    { value: 'oldest', label: t('sort.oldest') },
    { value: 'price-low', label: t('sort.priceLow') },
    { value: 'price-high', label: t('sort.priceHigh') },
    { value: 'name-asc', label: t('sort.nameAsc') },
    { value: 'name-desc', label: t('sort.nameDesc') },
    { value: 'rating', label: t('sort.rating') },
    { value: 'popular', label: t('sort.popular') },
  ];

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-600 whitespace-nowrap">
        {t('sort.label')}:
      </span>
      <Select value={currentSort} onValueChange={onSortChange}>
        <SelectTrigger className="w-48">
          <SelectValue placeholder={t('sort.placeholder')} />
        </SelectTrigger>
        <SelectContent>
          {sortOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
