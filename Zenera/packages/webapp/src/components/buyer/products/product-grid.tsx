"use client";

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Heart, ShoppingCart, Star, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { useCartStore } from '@/stores/cart-store';
import { cn } from '@/lib/utils';

interface Product {
  id: string;
  name: string;
  slug: string;
  base_price: number;
  sale_price?: number;
  images: string[];
  category: string;
  rating: number;
  reviews_count: number;
  in_stock: boolean;
}

interface ProductGridProps {
  products: Product[];
  viewMode: 'grid' | 'list';
  locale: string;
}

export function ProductGrid({ products, viewMode, locale }: ProductGridProps) {
  const { t } = useZeneraTranslation('products');
  const { addItem } = useCartStore();
  const [wishlist, setWishlist] = useState<Set<string>>(new Set());

  const toggleWishlist = (productId: string) => {
    const newWishlist = new Set(wishlist);
    if (newWishlist.has(productId)) {
      newWishlist.delete(productId);
    } else {
      newWishlist.add(productId);
    }
    setWishlist(newWishlist);
  };

  const handleAddToCart = async (product: Product) => {
    try {
      await addItem(product as any);
      // TODO: Show success toast
    } catch (error) {
      // TODO: Show error toast
      console.error('Failed to add to cart:', error);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat(locale === 'vi' ? 'vi-VN' : 'en-US', {
      style: 'currency',
      currency: locale === 'vi' ? 'VND' : 'USD',
    }).format(price);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          'h-4 w-4',
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        )}
      />
    ));
  };

  if (viewMode === 'list') {
    return (
      <div className="space-y-4">
        {products.map((product) => (
          <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <CardContent className="p-0">
              <div className="flex">
                {/* Product Image */}
                <div className="relative w-48 h-48 flex-shrink-0">
                  <Image
                    src={product.images[0]}
                    alt={product.name}
                    fill
                    className="object-cover"
                  />
                  {product.sale_price && (
                    <Badge className="absolute top-2 left-2 bg-red-500">
                      {t('badge.sale')}
                    </Badge>
                  )}
                  {!product.in_stock && (
                    <Badge className="absolute top-2 left-2 bg-gray-500">
                      {t('badge.outOfStock')}
                    </Badge>
                  )}
                </div>

                {/* Product Info */}
                <div className="flex-1 p-6">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <Badge variant="secondary" className="mb-2">
                        {product.category}
                      </Badge>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        <Link 
                          href={`/${locale}/products/${product.slug}`}
                          className="hover:text-blue-600 transition-colors"
                        >
                          {product.name}
                        </Link>
                      </h3>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleWishlist(product.id)}
                      className="text-gray-400 hover:text-red-500"
                    >
                      <Heart 
                        className={cn(
                          'h-5 w-5',
                          wishlist.has(product.id) && 'fill-current text-red-500'
                        )}
                      />
                    </Button>
                  </div>

                  {/* Rating */}
                  <div className="flex items-center gap-2 mb-3">
                    <div className="flex">
                      {renderStars(product.rating)}
                    </div>
                    <span className="text-sm text-gray-600">
                      {product.rating} ({product.reviews_count} {t('reviews')})
                    </span>
                  </div>

                  {/* Price */}
                  <div className="flex items-center gap-2 mb-4">
                    <span className="text-xl font-bold text-gray-900">
                      {formatPrice(product.sale_price || product.base_price)}
                    </span>
                    {product.sale_price && (
                      <span className="text-sm text-gray-500 line-through">
                        {formatPrice(product.base_price)}
                      </span>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button
                      onClick={() => handleAddToCart(product)}
                      disabled={!product.in_stock}
                      className="flex-1"
                    >
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      {product.in_stock ? t('actions.addToCart') : t('actions.outOfStock')}
                    </Button>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Grid view
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {products.map((product) => (
        <Card key={product.id} className="group overflow-hidden hover-lift border-0 shadow-md hover:shadow-2xl transition-all duration-300 bg-white rounded-xl">
          <CardContent className="p-0">
            {/* Product Image */}
            <div className="relative aspect-square overflow-hidden rounded-t-xl">
              <Image
                src={product.images[0]}
                alt={product.name}
                fill
                className="object-cover group-hover:scale-110 transition-transform duration-500"
              />
              {product.sale_price && (
                <Badge className="absolute top-3 left-3 bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg animate-pulse">
                  {t('badge.sale')}
                </Badge>
              )}
              {!product.in_stock && (
                <Badge className="absolute top-3 left-3 bg-gray-500 text-white">
                  {t('badge.outOfStock')}
                </Badge>
              )}

              {/* Gradient overlay on hover */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              
              {/* Hover Actions */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                <div className="flex gap-3">
                  <Button size="sm" className="glass text-white border-white/30 hover:bg-white/20 backdrop-blur-sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    className="glass text-white border-white/30 hover:bg-white/20 backdrop-blur-sm"
                    onClick={() => toggleWishlist(product.id)}
                  >
                    <Heart
                      className={cn(
                        'h-4 w-4',
                        wishlist.has(product.id) && 'fill-current text-red-400'
                      )}
                    />
                  </Button>
                </div>
              </div>
            </div>

            {/* Product Info */}
            <div className="p-5">
              <Badge variant="secondary" className="mb-3 text-xs bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border-0">
                {product.category}
              </Badge>

              <h3 className="font-semibold text-gray-900 mb-3 line-clamp-2 text-sm">
                <Link
                  href={`/${locale}/products/${product.slug}`}
                  className="hover:text-transparent hover:bg-gradient-to-r hover:from-blue-600 hover:to-purple-600 hover:bg-clip-text transition-all duration-300"
                >
                  {product.name}
                </Link>
              </h3>

              {/* Rating */}
              <div className="flex items-center gap-1 mb-3">
                <div className="flex">
                  {renderStars(product.rating)}
                </div>
                <span className="text-xs text-gray-600">
                  ({product.reviews_count})
                </span>
              </div>

              {/* Price */}
              <div className="flex items-center gap-2 mb-3">
                <span className="font-bold text-gray-900">
                  {formatPrice(product.sale_price || product.base_price)}
                </span>
                {product.sale_price && (
                  <span className="text-sm text-gray-500 line-through">
                    {formatPrice(product.base_price)}
                  </span>
                )}
              </div>

              {/* Add to Cart Button */}
              <Button
                onClick={() => handleAddToCart(product)}
                disabled={!product.in_stock}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                size="sm"
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                {product.in_stock ? t('actions.addToCart') : t('actions.outOfStock')}
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
