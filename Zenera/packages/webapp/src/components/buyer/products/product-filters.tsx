"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { X, RotateCcw } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface ProductFiltersProps {
  onFilterChange: (filters: any) => void;
  currentFilters: any;
}

export function ProductFilters({ onFilterChange, currentFilters }: ProductFiltersProps) {
  const { t } = useZeneraTranslation('products');
  const [priceRange, setPriceRange] = useState([0, 1000]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [inStockOnly, setInStockOnly] = useState(false);

  // Mock data - will be replaced with API calls
  const categories = [
    { id: 'electronics', name: 'Electronics', count: 156 },
    { id: 'fashion', name: 'Fashion', count: 89 },
    { id: 'home', name: 'Home & Garden', count: 67 },
    { id: 'sports', name: 'Sports & Outdoors', count: 45 },
    { id: 'books', name: 'Books', count: 234 },
    { id: 'toys', name: 'Toys & Games', count: 78 },
  ];

  const brands = [
    { id: 'apple', name: 'Apple', count: 23 },
    { id: 'samsung', name: 'Samsung', count: 18 },
    { id: 'nike', name: 'Nike', count: 34 },
    { id: 'adidas', name: 'Adidas', count: 28 },
    { id: 'sony', name: 'Sony', count: 15 },
  ];

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    const newCategories = checked
      ? [...selectedCategories, categoryId]
      : selectedCategories.filter(id => id !== categoryId);
    
    setSelectedCategories(newCategories);
    onFilterChange({ categories: newCategories });
  };

  const handleBrandChange = (brandId: string, checked: boolean) => {
    const newBrands = checked
      ? [...selectedBrands, brandId]
      : selectedBrands.filter(id => id !== brandId);
    
    setSelectedBrands(newBrands);
    onFilterChange({ brands: newBrands });
  };

  const handlePriceChange = (value: number[]) => {
    setPriceRange(value);
    onFilterChange({ priceMin: value[0], priceMax: value[1] });
  };

  const handleRatingChange = (rating: number) => {
    const newRating = selectedRating === rating ? null : rating;
    setSelectedRating(newRating);
    onFilterChange({ rating: newRating });
  };

  const handleInStockChange = (checked: boolean) => {
    setInStockOnly(checked);
    onFilterChange({ inStock: checked });
  };

  const clearAllFilters = () => {
    setSelectedCategories([]);
    setSelectedBrands([]);
    setPriceRange([0, 1000]);
    setSelectedRating(null);
    setInStockOnly(false);
    onFilterChange({});
  };

  const activeFiltersCount = selectedCategories.length + selectedBrands.length + 
    (selectedRating ? 1 : 0) + (inStockOnly ? 1 : 0);

  return (
    <div className="space-y-6">
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{t('filters.title')}</h3>
        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-red-600 hover:text-red-700"
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            {t('filters.clear')}
          </Button>
        )}
      </div>

      {/* Active Filters */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedCategories.map(categoryId => {
            const category = categories.find(c => c.id === categoryId);
            return (
              <Badge key={categoryId} variant="secondary" className="flex items-center gap-1">
                {category?.name}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleCategoryChange(categoryId, false)}
                />
              </Badge>
            );
          })}
          {selectedBrands.map(brandId => {
            const brand = brands.find(b => b.id === brandId);
            return (
              <Badge key={brandId} variant="secondary" className="flex items-center gap-1">
                {brand?.name}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleBrandChange(brandId, false)}
                />
              </Badge>
            );
          })}
          {selectedRating && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {selectedRating}+ ⭐
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleRatingChange(selectedRating)}
              />
            </Badge>
          )}
          {inStockOnly && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {t('filters.inStock')}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleInStockChange(false)}
              />
            </Badge>
          )}
        </div>
      )}

      {/* Price Range */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">{t('filters.price')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Slider
            value={priceRange}
            onValueChange={handlePriceChange}
            max={1000}
            step={10}
            className="w-full"
          />
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>${priceRange[0]}</span>
            <span>${priceRange[1]}</span>
          </div>
          <div className="flex gap-2">
            <Input
              type="number"
              placeholder="Min"
              value={priceRange[0]}
              onChange={(e) => handlePriceChange([parseInt(e.target.value) || 0, priceRange[1]])}
              className="text-sm"
            />
            <Input
              type="number"
              placeholder="Max"
              value={priceRange[1]}
              onChange={(e) => handlePriceChange([priceRange[0], parseInt(e.target.value) || 1000])}
              className="text-sm"
            />
          </div>
        </CardContent>
      </Card>

      {/* Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">{t('filters.categories')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {categories.map((category) => (
            <div key={category.id} className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={category.id}
                  checked={selectedCategories.includes(category.id)}
                  onCheckedChange={(checked) => handleCategoryChange(category.id, checked as boolean)}
                />
                <Label htmlFor={category.id} className="text-sm font-normal cursor-pointer">
                  {category.name}
                </Label>
              </div>
              <span className="text-xs text-gray-500">({category.count})</span>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Brands */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">{t('filters.brands')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {brands.map((brand) => (
            <div key={brand.id} className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={brand.id}
                  checked={selectedBrands.includes(brand.id)}
                  onCheckedChange={(checked) => handleBrandChange(brand.id, checked as boolean)}
                />
                <Label htmlFor={brand.id} className="text-sm font-normal cursor-pointer">
                  {brand.name}
                </Label>
              </div>
              <span className="text-xs text-gray-500">({brand.count})</span>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Rating */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">{t('filters.rating')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {[4, 3, 2, 1].map((rating) => (
            <div key={rating} className="flex items-center space-x-2">
              <Checkbox
                id={`rating-${rating}`}
                checked={selectedRating === rating}
                onCheckedChange={() => handleRatingChange(rating)}
              />
              <Label htmlFor={`rating-${rating}`} className="text-sm font-normal cursor-pointer flex items-center">
                {rating}+ ⭐ {t('filters.andUp')}
              </Label>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Availability */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">{t('filters.availability')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="in-stock"
              checked={inStockOnly}
              onCheckedChange={handleInStockChange}
            />
            <Label htmlFor="in-stock" className="text-sm font-normal cursor-pointer">
              {t('filters.inStock')}
            </Label>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
