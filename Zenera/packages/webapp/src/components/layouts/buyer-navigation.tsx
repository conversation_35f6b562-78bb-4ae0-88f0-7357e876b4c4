"use client";

import { useState } from "react";
import Link from "next/link";
import { ChevronDown, Grid, Tag, Heart, Truck } from "lucide-react";
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { useHTranslation } from "@/lib/i18n/hooks/use-h-translation";

interface BuyerNavigationProps {
  locale: string;
}

export function BuyerNavigation({ locale }: BuyerNavigationProps) {
  const { t } = useHTranslation('common');
  const [openCategory, setOpenCategory] = useState<string | null>(null);

  // Mock categories - sẽ được thay thế bằng API call
  const categories = [
    { id: '1', name: t('categories.electronics'), slug: 'electronics' },
    { id: '2', name: t('categories.fashion'), slug: 'fashion' },
    { id: '3', name: t('categories.home'), slug: 'home' },
    { id: '4', name: t('categories.books'), slug: 'books' },
    { id: '5', name: t('categories.sports'), slug: 'sports' },
  ];

  return (
    <nav className="bg-white dark:bg-gray-800 border-b shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex items-center space-x-8 py-3">
          {/* Categories Dropdown */}
          <DropdownMenu 
            open={openCategory === 'categories'} 
            onOpenChange={(open: boolean) => setOpenCategory(open ? 'categories' : null)}
          >
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className="flex items-center space-x-2"
                onMouseEnter={() => setOpenCategory('categories')}
              >
                <Grid className="h-4 w-4" />
                <span>{t('allCategories')}</span>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent 
              className="w-64"
              onMouseLeave={() => setOpenCategory(null)}
            >
              {categories.map((category) => (
                <DropdownMenuItem key={category.id} asChild>
                  <Link 
                    href={`/${locale}/buyer/categories/${category.slug}`}
                    className="flex items-center space-x-2 w-full"
                  >
                    <Tag className="h-4 w-4" />
                    <span>{category.name}</span>
                  </Link>
                </DropdownMenuItem>
              ))}
              <DropdownMenuItem asChild>
                <Link 
                  href={`/${locale}/buyer/categories`}
                  className="flex items-center space-x-2 w-full font-medium text-blue-600"
                >
                  <Grid className="h-4 w-4" />
                  <span>{t('viewAllCategories')}</span>
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Quick Links */}
          <div className="flex items-center space-x-6">
            <Link 
              href={`/${locale}/buyer/deals`}
              className="flex items-center space-x-1 text-red-600 hover:text-red-700 transition-colors"
            >
              <Tag className="h-4 w-4" />
              <span className="font-medium">{t('todaysDeals')}</span>
            </Link>

            <Link 
              href={`/${locale}/buyer/new-arrivals`}
              className="text-gray-700 dark:text-gray-300 hover:text-blue-600 transition-colors"
            >
              {t('newArrivals')}
            </Link>

            <Link 
              href={`/${locale}/buyer/bestsellers`}
              className="text-gray-700 dark:text-gray-300 hover:text-blue-600 transition-colors"
            >
              {t('bestsellers')}
            </Link>

            <Link 
              href={`/${locale}/buyer/wishlist`}
              className="flex items-center space-x-1 text-gray-700 dark:text-gray-300 hover:text-blue-600 transition-colors"
            >
              <Heart className="h-4 w-4" />
              <span>{t('wishlist')}</span>
            </Link>

            <Link 
              href={`/${locale}/buyer/track-order`}
              className="flex items-center space-x-1 text-gray-700 dark:text-gray-300 hover:text-blue-600 transition-colors"
            >
              <Truck className="h-4 w-4" />
              <span>{t('trackOrder')}</span>
            </Link>
          </div>

          {/* Right side - Promotions */}
          <div className="ml-auto">
            <span className="text-sm text-green-600 font-medium">
              {t('freeShippingOver50')}
            </span>
          </div>
        </div>
      </div>
    </nav>
  );
}
