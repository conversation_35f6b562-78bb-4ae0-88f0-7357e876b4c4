"use client";

import { useEffect, useState } from "react";
import { I18nextProvider } from "react-i18next";
import i18next from "@/lib/i18n/client";

/**
 * i18n Provider Component
 * Provides i18next instance to the app với locale support
 */
interface I18nProviderProps {
  children: React.ReactNode;
  locale?: string;
}

export function I18nProvider({ children, locale = 'en' }: I18nProviderProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);

    // Initialize i18next if not already done
    if (!i18next.isInitialized) {
      console.log("Initializing i18next...");
    }

    // Change language if different from current
    if (i18next.language !== locale) {
      console.log(`Changing language to: ${locale}`);
      i18next.changeLanguage(locale);
    }
  }, [locale]);

  // Prevent hydration mismatch by not rendering until client-side
  if (!isClient) {
    return <div>{children}</div>;
  }

  return (
    <I18nextProvider i18n={i18next}>
      {children}
    </I18nextProvider>
  );
}
