"use client";

import React, { useState } from 'react';

const StylesDemo: React.FC = () => {
  const [currentTheme, setCurrentTheme] = useState('zenera-theme');
  const [animationElement, setAnimationElement] = useState('metal');

  const themes = [
    { name: 'Default', class: 'zenera-theme' },
    { name: 'Professional', class: 'zenera-theme zenera-theme-professional' },
    { name: 'Creative', class: 'zenera-theme zenera-theme-creative' },
    { name: 'Minimal', class: 'zenera-theme zenera-theme-minimal' },
    { name: 'Luxury', class: 'zenera-theme zenera-theme-luxury' },
    { name: 'E-commerce', class: 'zenera-theme zenera-theme-ecommerce' },
  ];

  const animations = [
    { name: 'Metal', class: 'zenera-animate-metal-enter' },
    { name: 'Wood', class: 'zenera-animate-wood-grow' },
    { name: 'Water', class: 'zenera-animate-water-flow' },
    { name: 'Fire', class: 'zenera-animate-fire-burst' },
    { name: 'Earth', class: 'zenera-animate-earth-settle' },
    { name: 'Crystal', class: 'zenera-animate-crystal-form' },
    { name: 'Blade Cut', class: 'zenera-animate-blade-cut' },
  ];

  const hoverEffects = [
    { name: 'Metal Hover', class: 'zenera-hover-metal' },
    { name: 'Crystal Hover', class: 'zenera-hover-crystal' },
    { name: 'Water Hover', class: 'zenera-hover-water' },
    { name: 'Fire Hover', class: 'zenera-hover-fire' },
    { name: 'Earth Hover', class: 'zenera-hover-earth' },
  ];

  return (
    <div className={currentTheme}>
      <div className="zenera-container zenera-py-8">
        <div className="zenera-mb-8">
          <h1 className="zenera-heading zenera-h1 zenera-mb-4">
            Zenera Design System Demo
          </h1>
          <p className="zenera-text zenera-text-secondary zenera-text-lg">
            Explore our unique Five Elements inspired design system with angular animations and flexible theming.
          </p>
        </div>

        {/* Theme Switcher */}
        <div className="zenera-card zenera-mb-8">
          <h2 className="zenera-heading zenera-h3 zenera-mb-4">Theme Switcher</h2>
          <div className="zenera-flex zenera-flex-wrap zenera-gap-2">
            {themes.map((theme) => (
              <button
                key={theme.name}
                onClick={() => setCurrentTheme(theme.class)}
                className={`zenera-btn ${
                  currentTheme === theme.class ? 'zenera-btn-primary' : 'zenera-btn-outline'
                }`}
              >
                {theme.name}
              </button>
            ))}
          </div>
        </div>

        {/* Typography Demo */}
        <div className="zenera-card zenera-mb-8">
          <h2 className="zenera-heading zenera-h3 zenera-mb-4">Typography</h2>
          <div className="zenera-grid zenera-grid-cols-1 zenera-md-grid-cols-2 zenera-gap-6">
            <div>
              <h1 className="zenera-heading zenera-h1">Heading 1</h1>
              <h2 className="zenera-heading zenera-h2">Heading 2</h2>
              <h3 className="zenera-heading zenera-h3">Heading 3</h3>
              <h4 className="zenera-heading zenera-h4">Heading 4</h4>
              <h5 className="zenera-heading zenera-h5">Heading 5</h5>
              <h6 className="zenera-heading zenera-h6">Heading 6</h6>
            </div>
            <div>
              <p className="zenera-text zenera-mb-4">
                This is regular body text with normal weight and line height.
              </p>
              <p className="zenera-text zenera-text-secondary zenera-mb-4">
                This is secondary text with reduced opacity for less emphasis.
              </p>
              <p className="zenera-text zenera-text-tertiary zenera-mb-4">
                This is tertiary text for subtle information.
              </p>
              <a href="#" className="zenera-link">
                This is a link with hover effects
              </a>
            </div>
          </div>
        </div>

        {/* Button Demo */}
        <div className="zenera-card zenera-mb-8">
          <h2 className="zenera-heading zenera-h3 zenera-mb-4">Buttons</h2>
          <div className="zenera-flex zenera-flex-wrap zenera-gap-4 zenera-mb-4">
            <button className="zenera-btn zenera-btn-primary">Primary Button</button>
            <button className="zenera-btn zenera-btn-secondary">Secondary Button</button>
            <button className="zenera-btn zenera-btn-outline">Outline Button</button>
            <button className="zenera-btn zenera-btn-ghost">Ghost Button</button>
          </div>
          <div className="zenera-flex zenera-flex-wrap zenera-gap-4">
            <button className="zenera-btn zenera-btn-primary zenera-btn-sm">Small</button>
            <button className="zenera-btn zenera-btn-primary">Regular</button>
            <button className="zenera-btn zenera-btn-primary zenera-btn-lg">Large</button>
            <button className="zenera-btn zenera-btn-primary" disabled>Disabled</button>
          </div>
        </div>

        {/* Animation Demo */}
        <div className="zenera-card zenera-mb-8">
          <h2 className="zenera-heading zenera-h3 zenera-mb-4">Five Elements Animations</h2>
          <div className="zenera-mb-4">
            <p className="zenera-text zenera-text-secondary zenera-mb-2">
              Click an animation to see it in action:
            </p>
            <div className="zenera-flex zenera-flex-wrap zenera-gap-2">
              {animations.map((animation) => (
                <button
                  key={animation.name}
                  onClick={() => {
                    const element = document.getElementById('animation-demo');
                    if (element) {
                      element.className = 'zenera-card zenera-p-8 zenera-text-center';
                      setTimeout(() => {
                        element.className = `zenera-card zenera-p-8 zenera-text-center ${animation.class}`;
                      }, 50);
                    }
                  }}
                  className="zenera-btn zenera-btn-outline zenera-btn-sm"
                >
                  {animation.name}
                </button>
              ))}
            </div>
          </div>
          <div
            id="animation-demo"
            className="zenera-card zenera-p-8 zenera-text-center"
          >
            <div className="zenera-text-6xl zenera-mb-4">⚡</div>
            <p className="zenera-text">Animation Demo Area</p>
            <p className="zenera-text zenera-text-secondary zenera-text-sm">
              Click an animation button above to see the effect
            </p>
          </div>
        </div>

        {/* Hover Effects Demo */}
        <div className="zenera-card zenera-mb-8">
          <h2 className="zenera-heading zenera-h3 zenera-mb-4">Hover Effects</h2>
          <div className="zenera-grid zenera-grid-cols-1 zenera-md-grid-cols-3 zenera-lg-grid-cols-5 zenera-gap-4">
            {hoverEffects.map((effect) => (
              <div
                key={effect.name}
                className={`zenera-card zenera-p-4 zenera-text-center ${effect.class}`}
              >
                <div className="zenera-text-2xl zenera-mb-2">🎯</div>
                <p className="zenera-text zenera-text-sm">{effect.name}</p>
              </div>
            ))}
          </div>
        </div>

        {/* E-commerce Components Demo */}
        <div className="zenera-card zenera-mb-8">
          <h2 className="zenera-heading zenera-h3 zenera-mb-4">E-commerce Components</h2>
          <div className="zenera-grid zenera-grid-cols-1 zenera-md-grid-cols-2 zenera-lg-grid-cols-3 zenera-gap-6">
            {/* Product Card */}
            <div className="zenera-product-card zenera-card">
              <div className="zenera-product-image zenera-bg-secondary zenera-h-48 zenera-rounded-md zenera-mb-4 zenera-flex zenera-items-center zenera-justify-center">
                <span className="zenera-text-4xl">📱</span>
              </div>
              <h3 className="zenera-heading zenera-h5 zenera-mb-2">Sample Product</h3>
              <p className="zenera-text zenera-text-secondary zenera-mb-4 zenera-line-clamp-2">
                This is a sample product description that shows how text truncation works.
              </p>
              <div className="zenera-flex zenera-items-center zenera-justify-between zenera-mb-4">
                <div>
                  <span className="zenera-price zenera-text-lg zenera-font-bold">$99.99</span>
                  <span className="zenera-text zenera-text-tertiary zenera-line-through zenera-ml-2">$129.99</span>
                </div>
                <span className="zenera-discount-badge">-23%</span>
              </div>
              <button className="zenera-add-to-cart-btn zenera-btn zenera-btn-primary zenera-w-full">
                Add to Cart
              </button>
            </div>

            {/* Another Product Card */}
            <div className="zenera-product-card zenera-card">
              <div className="zenera-product-image zenera-bg-secondary zenera-h-48 zenera-rounded-md zenera-mb-4 zenera-flex zenera-items-center zenera-justify-center">
                <span className="zenera-text-4xl">💻</span>
              </div>
              <h3 className="zenera-heading zenera-h5 zenera-mb-2">Another Product</h3>
              <p className="zenera-text zenera-text-secondary zenera-mb-4 zenera-line-clamp-2">
                Another sample product with different styling and effects.
              </p>
              <div className="zenera-flex zenera-items-center zenera-justify-between zenera-mb-4">
                <span className="zenera-price zenera-text-lg zenera-font-bold">$199.99</span>
              </div>
              <button className="zenera-add-to-cart-btn zenera-btn zenera-btn-primary zenera-w-full">
                Add to Cart
              </button>
            </div>

            {/* Loading State */}
            <div className="zenera-card">
              <div className="zenera-skeleton-loading zenera-h-48 zenera-rounded-md zenera-mb-4"></div>
              <div className="zenera-skeleton-loading zenera-h-4 zenera-rounded zenera-mb-2"></div>
              <div className="zenera-skeleton-loading zenera-h-4 zenera-rounded zenera-w-3/4 zenera-mb-4"></div>
              <div className="zenera-skeleton-loading zenera-h-8 zenera-rounded"></div>
            </div>
          </div>
        </div>

        {/* Form Demo */}
        <div className="zenera-card">
          <h2 className="zenera-heading zenera-h3 zenera-mb-4">Form Elements</h2>
          <div className="zenera-grid zenera-grid-cols-1 zenera-md-grid-cols-2 zenera-gap-6">
            <div>
              <label className="zenera-text zenera-font-medium zenera-mb-2 zenera-block">
                Email Address
              </label>
              <input
                type="email"
                placeholder="Enter your email"
                className="zenera-input zenera-mb-4"
              />
              
              <label className="zenera-text zenera-font-medium zenera-mb-2 zenera-block">
                Message
              </label>
              <textarea
                placeholder="Enter your message"
                rows={4}
                className="zenera-input zenera-mb-4"
              />
              
              <button className="zenera-btn zenera-btn-primary">
                Submit Form
              </button>
            </div>
            
            <div>
              <label className="zenera-text zenera-font-medium zenera-mb-2 zenera-block">
                Product Category
              </label>
              <select className="zenera-input zenera-mb-4">
                <option>Electronics</option>
                <option>Clothing</option>
                <option>Home & Garden</option>
              </select>
              
              <div className="zenera-flex zenera-items-center zenera-gap-2 zenera-mb-4">
                <input type="checkbox" id="newsletter" />
                <label htmlFor="newsletter" className="zenera-text">
                  Subscribe to newsletter
                </label>
              </div>
              
              <div className="zenera-flex zenera-gap-2">
                <button className="zenera-btn zenera-btn-outline">Cancel</button>
                <button className="zenera-btn zenera-btn-primary">Save</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StylesDemo;
